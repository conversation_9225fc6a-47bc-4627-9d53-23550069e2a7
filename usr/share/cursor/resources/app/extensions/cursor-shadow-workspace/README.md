# Cursor Shadow Workspace

**Notice:** This extension is bundled with Visual Studio Code. It can be disabled but not uninstalled.

A built-in extension that manages a shadow version of your workspace that AI agents can use to refine their code before showing it to you.

The shadow workspace is a hidden window running on your local machine in a copy of your current workspace.

To turn off the shadow workspace, set `cursor.general.enableShadowWorkspace` to `false`.