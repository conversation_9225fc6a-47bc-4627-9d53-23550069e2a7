{"extends": "../tsconfig.base.json", "compilerOptions": {"outDir": "./out", "types": ["node"], "esModuleInterop": true, "paths": {"proto/*": ["./src/proto/*"], "external/bufbuild/protobuf": ["./node_modules/@bufbuild/protobuf"], "external/bufbuild/connect": ["./node_modules/@connectrpc/connect"], "@cursor/types": ["./src/gen/reactiveStorageTypes"]}, "rootDir": "./src"}, "include": ["src/**/*", "@cursor/types", "../../src/vscode-dts/vscode.d.ts", "../../src/vscode-dts/vscode.proposed.inlineCompletionsAdditions.d.ts", "../../src/vscode-dts/vscode.proposed.control.d.ts", "../../src/vscode-dts/vscode.proposed.cursor.d.ts", "../git/src/api/git.d.ts"]}