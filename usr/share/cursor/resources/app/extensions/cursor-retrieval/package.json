{"name": "cursor-retrieval", "description": "Handles indexing and retrieval for Cursor", "author": "Anysphere, Inc.", "publisher": "anysphere", "version": "0.0.1", "workspaces": ["packages/file-service", "packages/git-graph-local"], "private": true, "repository": {"type": "git", "url": "https://github.com/anysphere/vscode"}, "extensionKind": ["workspace"], "engines": {"vscode": "^1.43.0", "yarn": "please-use-npm"}, "activationEvents": ["onStartupFinished"], "enabledApiProposals": ["control", "cursor"], "main": "./dist/main", "contributes": {"commands": [], "keybindings": [], "menus": {}, "configuration": {"type": "object", "title": "Cursor Retrieval", "properties": {"cursor-retrieval.canAttemptGithubLogin": {"type": "boolean", "default": true, "description": "Whether or not <PERSON><PERSON><PERSON> should attempt github login to augment retrieval results", "scope": "resource"}}}, "languages": [{"id": "ignore", "filenames": [".cursorignore"]}, {"id": "ignore", "filenames": [".cursorinde<PERSON><PERSON><PERSON>"]}]}}