
!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},n=(new e.Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="d33b210a-387a-5b77-8b6e-5593aeffdaac")}catch(e){}}();
exports.id=868,exports.ids=[868],exports.modules={7042:t=>{function e(t){return Buffer.isBuffer(t)?t:Buffer.from(t.buffer,t.byteOffset,t.byteLength)}t.exports={isBuffer:function(t){return Buffer.isBuffer(t)||t instanceof Uint8Array},isEncoding:function(t){return Buffer.isEncoding(t)},alloc:function(t,e,i){return Buffer.alloc(t,e,i)},allocUnsafe:function(t){return Buffer.allocUnsafe(t)},allocUnsafeSlow:function(t){return Buffer.allocUnsafeSlow(t)},byteLength:function(t,e){return Buffer.byteLength(t,e)},compare:function(t,e){return Buffer.compare(t,e)},concat:function(t,e){return Buffer.concat(t,e)},copy:function(t,i,n,r,s){return e(t).copy(i,n,r,s)},equals:function(t,i){return e(t).equals(i)},fill:function(t,i,n,r,s){return e(t).fill(i,n,r,s)},from:function(t,e,i){return Buffer.from(t,e,i)},includes:function(t,i,n,r){return e(t).includes(i,n,r)},indexOf:function(t,i,n,r){return e(t).indexOf(i,n,r)},lastIndexOf:function(t,i,n,r){return e(t).lastIndexOf(i,n,r)},swap16:function(t){return e(t).swap16()},swap32:function(t){return e(t).swap32()},swap64:function(t){return e(t).swap64()},toBuffer:e,toString:function(t,i,n,r){return e(t).toString(i,n,r)},write:function(t,i,n,r,s){return e(t).write(i,n,r,s)},writeDoubleLE:function(t,i,n){return e(t).writeDoubleLE(i,n)},writeFloatLE:function(t,i,n){return e(t).writeFloatLE(i,n)},writeUInt32LE:function(t,i,n){return e(t).writeUInt32LE(i,n)},writeInt32LE:function(t,i,n){return e(t).writeInt32LE(i,n)},readDoubleLE:function(t,i){return e(t).readDoubleLE(i)},readFloatLE:function(t,i){return e(t).readFloatLE(i)},readUInt32LE:function(t,i){return e(t).readUInt32LE(i)},readInt32LE:function(t,i){return e(t).readInt32LE(i)},writeDoubleBE:function(t,i,n){return e(t).writeDoubleBE(i,n)},writeFloatBE:function(t,i,n){return e(t).writeFloatBE(i,n)},writeUInt32BE:function(t,i,n){return e(t).writeUInt32BE(i,n)},writeInt32BE:function(t,i,n){return e(t).writeInt32BE(i,n)},readDoubleBE:function(t,i){return e(t).readDoubleBE(i)},readFloatBE:function(t,i){return e(t).readFloatBE(i)},readUInt32BE:function(t,i){return e(t).readUInt32BE(i)},readInt32BE:function(t,i){return e(t).readInt32BE(i)}}},8992:(t,e,i)=>{var n=i(3519),r=function(){},s=function(t,e,i){if("function"==typeof e)return s(t,null,e);e||(e={}),i=n(i||r);var a=t._writableState,u=t._readableState,o=e.readable||!1!==e.readable&&t.readable,l=e.writable||!1!==e.writable&&t.writable,h=!1,d=function(){t.writable||c()},c=function(){l=!1,o||i.call(t)},f=function(){o=!1,l||i.call(t)},p=function(e){i.call(t,e?new Error("exited with error code: "+e):null)},_=function(e){i.call(t,e)},m=function(){process.nextTick(b)},b=function(){if(!h)return(!o||u&&u.ended&&!u.destroyed)&&(!l||a&&a.ended&&!a.destroyed)?void 0:i.call(t,new Error("premature close"))},S=function(){t.req.on("finish",c)};return function(t){return t.setHeader&&"function"==typeof t.abort}(t)?(t.on("complete",c),t.on("abort",m),t.req?S():t.on("request",S)):l&&!a&&(t.on("end",d),t.on("close",d)),function(t){return t.stdio&&Array.isArray(t.stdio)&&3===t.stdio.length}(t)&&t.on("exit",p),t.on("end",f),t.on("finish",c),!1!==e.error&&t.on("error",_),t.on("close",m),function(){h=!0,t.removeListener("complete",c),t.removeListener("abort",m),t.removeListener("request",S),t.req&&t.req.removeListener("finish",c),t.removeListener("end",d),t.removeListener("close",d),t.removeListener("finish",c),t.removeListener("exit",p),t.removeListener("end",f),t.removeListener("error",_),t.removeListener("close",m)}};t.exports=s},8190:t=>{t.exports=class{constructor(t){if(!(t>0)||t-1&t)throw new Error("Max size for a FixedFIFO should be a power of two");this.buffer=new Array(t),this.mask=t-1,this.top=0,this.btm=0,this.next=null}clear(){this.top=this.btm=0,this.next=null,this.buffer.fill(void 0)}push(t){return void 0===this.buffer[this.top]&&(this.buffer[this.top]=t,this.top=this.top+1&this.mask,!0)}shift(){const t=this.buffer[this.btm];if(void 0!==t)return this.buffer[this.btm]=void 0,this.btm=this.btm+1&this.mask,t}peek(){return this.buffer[this.btm]}isEmpty(){return void 0===this.buffer[this.btm]}}},6080:(t,e,i)=>{const n=i(8190);t.exports=class{constructor(t){this.hwm=t||16,this.head=new n(this.hwm),this.tail=this.head,this.length=0}clear(){this.head=this.tail,this.head.clear(),this.length=0}push(t){if(this.length++,!this.head.push(t)){const e=this.head;this.head=e.next=new n(2*this.head.buffer.length),this.head.push(t)}}shift(){0!==this.length&&this.length--;const t=this.tail.shift();if(void 0===t&&this.tail.next){const t=this.tail.next;return this.tail.next=null,this.tail=t,this.tail.shift()}return t}peek(){const t=this.tail.peek();return void 0===t&&this.tail.next?this.tail.next.peek():t}isEmpty(){return 0===this.length}}},815:(t,e,i)=>{var n,r=i(3519),s=i(8992);try{n=i(9896)}catch(t){}var a=function(){},u=/^v?\.0/.test(process.version),o=function(t){return"function"==typeof t},l=function(t){t()},h=function(t,e){return t.pipe(e)};t.exports=function(){var t,e=Array.prototype.slice.call(arguments),i=o(e[e.length-1]||a)&&e.pop()||a;if(Array.isArray(e[0])&&(e=e[0]),e.length<2)throw new Error("pump requires two streams per minimum");var d=e.map((function(h,c){var f=c<e.length-1;return function(t,e,i,l){l=r(l);var h=!1;t.on("close",(function(){h=!0})),s(t,{readable:e,writable:i},(function(t){if(t)return l(t);h=!0,l()}));var d=!1;return function(e){if(!h&&!d)return d=!0,function(t){return!!u&&!!n&&(t instanceof(n.ReadStream||a)||t instanceof(n.WriteStream||a))&&o(t.close)}(t)?t.close(a):function(t){return t.setHeader&&o(t.abort)}(t)?t.abort():o(t.destroy)?t.destroy():void l(e||new Error("stream was destroyed"))}}(h,f,c>0,(function(e){t||(t=e),e&&d.forEach(l),f||(d.forEach(l),i(t))}))}));return e.reduce(h)}},4111:(t,e,i)=>{t.exports="undefined"!=typeof process&&"function"==typeof process.nextTick?process.nextTick.bind(process):i(2133)},2133:t=>{t.exports="function"==typeof queueMicrotask?queueMicrotask:t=>Promise.resolve().then(t)},8179:(t,e,i)=>{const{EventEmitter:n}=i(4434),r=new Error("Stream was destroyed"),s=new Error("Premature close"),a=i(4111),u=i(6080),o=i(5335),l=536870911,h=1^l,d=2^l,c=16384,f=32768,p=131072,_=16^l,m=536805375,b=768^l,S=536838143,y=536739839,g=2<<18,x=4<<18,w=8<<18,k=32<<18,v=64<<18,L=128<<18,E=512<<18,P=1024<<18,B=503316479,I=268435455,T=262160,N=8404992,z=8405006,F=33587200,W=33587215,q=270794767,M=Symbol.asyncIterator||Symbol("asyncIterator");class U{constructor(t,{highWaterMark:e=16384,map:i=null,mapWritable:n,byteLength:r,byteLengthWritable:s}={}){this.stream=t,this.queue=new u,this.highWaterMark=e,this.buffered=0,this.error=null,this.pipeline=null,this.drains=null,this.byteLength=s||r||ut,this.map=n||i,this.afterWrite=R.bind(this),this.afterUpdateNextTick=V.bind(this)}get ended(){return!!(this.stream._duplexState&k)}push(t){return!(142606350&this.stream._duplexState||(null!==this.map&&(t=this.map(t)),this.buffered+=this.byteLength(t),this.queue.push(t),this.buffered<this.highWaterMark?(this.stream._duplexState|=w,0):(this.stream._duplexState|=6291456,1)))}shift(){const t=this.queue.shift();return this.buffered-=this.byteLength(t),0===this.buffered&&(this.stream._duplexState&=534773759),t}end(t){"function"==typeof t?this.stream.once("finish",t):null!=t&&this.push(t),this.stream._duplexState=535822335&this.stream._duplexState|134217728}autoBatch(t,e){const i=[],n=this.stream;for(i.push(t);2359296==(n._duplexState&q);)i.push(n._writableState.shift());if(15&n._duplexState)return e(null);n._writev(i,e)}update(){const t=this.stream;t._duplexState|=g;do{for(;(t._duplexState&q)===w;){const e=this.shift();t._duplexState|=67371008,t._write(e,this.afterWrite)}1310720&t._duplexState||this.updateNonPrimary()}while(!0===this.continueUpdate());t._duplexState&=536346623}updateNonPrimary(){const t=this.stream;if((144965647&t._duplexState)===E)return t._duplexState=262144|t._duplexState,void t._final(C.bind(this));4!=(14&t._duplexState)?1==(t._duplexState&W)&&(t._duplexState=(t._duplexState|T)&h,t._open(J.bind(this))):t._duplexState&F||(t._duplexState|=T,t._destroy(H.bind(this)))}continueUpdate(){return!!(this.stream._duplexState&L)&&(this.stream._duplexState&=B,!0)}updateCallback(){(35127311&this.stream._duplexState)===x?this.update():this.updateNextTick()}updateNextTick(){this.stream._duplexState&L||(this.stream._duplexState|=L,this.stream._duplexState&g||a(this.afterUpdateNextTick))}}class O{constructor(t,{highWaterMark:e=16384,map:i=null,mapReadable:n,byteLength:r,byteLengthReadable:s}={}){this.stream=t,this.queue=new u,this.highWaterMark=0===e?1:e,this.buffered=0,this.readAhead=e>0,this.error=null,this.pipeline=null,this.byteLength=s||r||ut,this.map=n||i,this.pipeTo=null,this.afterRead=G.bind(this),this.afterUpdateNextTick=K.bind(this)}get ended(){return!!(this.stream._duplexState&c)}pipe(t,e){if(null!==this.pipeTo)throw new Error("Can only pipe to one destination");if("function"!=typeof e&&(e=null),this.stream._duplexState|=512,this.pipeTo=t,this.pipeline=new A(this.stream,t,e),e&&this.stream.on("error",ot),at(t))t._writableState.pipeline=this.pipeline,e&&t.on("error",ot),t.on("finish",this.pipeline.finished.bind(this.pipeline));else{const e=this.pipeline.done.bind(this.pipeline,t),i=this.pipeline.done.bind(this.pipeline,t,null);t.on("error",e),t.on("close",i),t.on("finish",this.pipeline.finished.bind(this.pipeline))}t.on("drain",j.bind(this)),this.stream.emit("piping",t),t.emit("pipe",this.stream)}push(t){const e=this.stream;return null===t?(this.highWaterMark=0,e._duplexState=536805311&e._duplexState|1024,!1):null!==this.map&&null===(t=this.map(t))?(e._duplexState&=m,this.buffered<this.highWaterMark):(this.buffered+=this.byteLength(t),this.queue.push(t),e._duplexState=(128|e._duplexState)&m,this.buffered<this.highWaterMark)}shift(){const t=this.queue.shift();return this.buffered-=this.byteLength(t),0===this.buffered&&(this.stream._duplexState&=536862591),t}unshift(t){const e=[null!==this.map?this.map(t):t];for(;this.buffered>0;)e.push(this.shift());for(let t=0;t<e.length-1;t++){const i=e[t];this.buffered+=this.byteLength(i),this.queue.push(i)}this.push(e[e.length-1])}read(){const t=this.stream;if(128==(16527&t._duplexState)){const e=this.shift();return null!==this.pipeTo&&!1===this.pipeTo.write(e)&&(t._duplexState&=b),2048&t._duplexState&&t.emit("data",e),e}return!1===this.readAhead&&(t._duplexState|=p,this.updateNextTick()),null}drain(){const t=this.stream;for(;128==(16527&t._duplexState)&&768&t._duplexState;){const e=this.shift();null!==this.pipeTo&&!1===this.pipeTo.write(e)&&(t._duplexState&=b),2048&t._duplexState&&t.emit("data",e)}}update(){const t=this.stream;t._duplexState|=32;do{for(this.drain();this.buffered<this.highWaterMark&&(214047&t._duplexState)===p;)t._duplexState|=65552,t._read(this.afterRead),this.drain();4224==(12431&t._duplexState)&&(t._duplexState|=8192,t.emit("readable")),80&t._duplexState||this.updateNonPrimary()}while(!0===this.continueUpdate());t._duplexState&=536870879}updateNonPrimary(){const t=this.stream;1024==(1167&t._duplexState)&&(t._duplexState=536869887&t._duplexState|16384,t.emit("end"),(t._duplexState&z)===N&&(t._duplexState|=4),null!==this.pipeTo&&this.pipeTo.end()),4!=(14&t._duplexState)?1==(t._duplexState&W)&&(t._duplexState=(t._duplexState|T)&h,t._open(J.bind(this))):t._duplexState&F||(t._duplexState|=T,t._destroy(H.bind(this)))}continueUpdate(){return!!(this.stream._duplexState&f)&&(this.stream._duplexState&=S,!0)}updateCallback(){64==(32879&this.stream._duplexState)?this.update():this.updateNextTick()}updateNextTickIfOpen(){32769&this.stream._duplexState||(this.stream._duplexState|=f,32&this.stream._duplexState||a(this.afterUpdateNextTick))}updateNextTick(){this.stream._duplexState&f||(this.stream._duplexState|=f,32&this.stream._duplexState||a(this.afterUpdateNextTick))}}class D{constructor(t){this.data=null,this.afterTransform=Q.bind(t),this.afterFinal=null}}class A{constructor(t,e,i){this.from=t,this.to=e,this.afterPipe=i,this.error=null,this.pipeToFinished=!1}finished(){this.pipeToFinished=!0}done(t,e){e&&(this.error=e),t!==this.to||(this.to=null,null===this.from)?t!==this.from||(this.from=null,null===this.to)?(null!==this.afterPipe&&this.afterPipe(this.error),this.to=this.from=this.afterPipe=null):t._duplexState&c||this.to.destroy(this.error||new Error("Readable stream closed before ending")):this.from._duplexState&c&&this.pipeToFinished||this.from.destroy(this.error||new Error("Writable stream closed prematurely"))}}function j(){this.stream._duplexState|=512,this.updateCallback()}function C(t){const e=this.stream;t&&e.destroy(t),14&e._duplexState||(e._duplexState|=k,e.emit("finish")),(e._duplexState&z)===N&&(e._duplexState|=4),e._duplexState&=402391039,e._duplexState&g?this.updateNextTick():this.update()}function H(t){const e=this.stream;t||this.error===r||(t=this.error),t&&e.emit("error",t),e._duplexState|=8,e.emit("close");const i=e._readableState,n=e._writableState;if(null!==i&&null!==i.pipeline&&i.pipeline.done(e,t),null!==n){for(;null!==n.drains&&n.drains.length>0;)n.drains.shift().resolve(!1);null!==n.pipeline&&n.pipeline.done(e,t)}}function R(t){const e=this.stream;t&&e.destroy(t),e._duplexState&=469499903,null!==this.drains&&function(t){for(let e=0;e<t.length;e++)0==--t[e].writes&&(t.shift().resolve(!0),e--)}(this.drains),4194304==(6553615&e._duplexState)&&(e._duplexState&=532676607,(e._duplexState&v)===v&&e.emit("drain")),this.updateCallback()}function G(t){t&&this.stream.destroy(t),this.stream._duplexState&=_,!1!==this.readAhead||256&this.stream._duplexState||(this.stream._duplexState&=y),this.updateCallback()}function K(){32&this.stream._duplexState||(this.stream._duplexState&=S,this.update())}function V(){this.stream._duplexState&g||(this.stream._duplexState&=B,this.update())}function J(t){const e=this.stream;t&&e.destroy(t),4&e._duplexState||(17423&e._duplexState||(e._duplexState|=64),142606351&e._duplexState||(e._duplexState|=x),e.emit("open")),e._duplexState&=536608751,null!==e._writableState&&e._writableState.updateCallback(),null!==e._readableState&&e._readableState.updateCallback()}function Q(t,e){null!=e&&this.push(e),this._writableState.afterWrite(t)}function X(t){null!==this._readableState&&("data"===t&&(this._duplexState|=133376,this._readableState.updateNextTick()),"readable"===t&&(this._duplexState|=4096,this._readableState.updateNextTick())),null!==this._writableState&&"drain"===t&&(this._duplexState|=v,this._writableState.updateNextTick())}class Y extends n{constructor(t){super(),this._duplexState=0,this._readableState=null,this._writableState=null,t&&(t.open&&(this._open=t.open),t.destroy&&(this._destroy=t.destroy),t.predestroy&&(this._predestroy=t.predestroy),t.signal&&t.signal.addEventListener("abort",lt.bind(this))),this.on("newListener",X)}_open(t){t(null)}_destroy(t){t(null)}_predestroy(){}get readable(){return null!==this._readableState||void 0}get writable(){return null!==this._writableState||void 0}get destroyed(){return!!(8&this._duplexState)}get destroying(){return!!(14&this._duplexState)}destroy(t){14&this._duplexState||(t||(t=r),this._duplexState=535822271&this._duplexState|4,null!==this._readableState&&(this._readableState.highWaterMark=0,this._readableState.error=t),null!==this._writableState&&(this._writableState.highWaterMark=0,this._writableState.error=t),this._duplexState|=2,this._predestroy(),this._duplexState&=d,null!==this._readableState&&this._readableState.updateNextTick(),null!==this._writableState&&this._writableState.updateNextTick())}}class Z extends Y{constructor(t){super(t),this._duplexState|=8519681,this._readableState=new O(this,t),t&&(!1===this._readableState.readAhead&&(this._duplexState&=y),t.read&&(this._read=t.read),t.eagerOpen&&this._readableState.updateNextTick(),t.encoding&&this.setEncoding(t.encoding))}setEncoding(t){const e=new o(t),i=this._readableState.map||rt;return this._readableState.map=function(t){const n=e.push(t);return""===n&&(0!==t.byteLength||e.remaining>0)?null:i(n)},this}_read(t){t(null)}pipe(t,e){return this._readableState.updateNextTick(),this._readableState.pipe(t,e),t}read(){return this._readableState.updateNextTick(),this._readableState.read()}push(t){return this._readableState.updateNextTickIfOpen(),this._readableState.push(t)}unshift(t){return this._readableState.updateNextTickIfOpen(),this._readableState.unshift(t)}resume(){return this._duplexState|=131328,this._readableState.updateNextTick(),this}pause(){return this._duplexState&=!1===this._readableState.readAhead?536739583:536870655,this}static _fromAsyncIterator(t,e){let i;const n=new Z({...e,read(e){t.next().then(r).then(e.bind(null,null)).catch(e)},predestroy(){i=t.return()},destroy(t){if(!i)return t(null);i.then(t.bind(null,null)).catch(t)}});return n;function r(t){t.done?n.push(null):n.push(t.value)}}static from(t,e){if(at(i=t)&&i.readable)return t;var i;if(t[M])return this._fromAsyncIterator(t[M](),e);Array.isArray(t)||(t=void 0===t?[]:[t]);let n=0;return new Z({...e,read(e){this.push(n===t.length?null:t[n++]),e(null)}})}static isBackpressured(t){return!!(17422&t._duplexState)||t._readableState.buffered>=t._readableState.highWaterMark}static isPaused(t){return!(256&t._duplexState)}[M](){const t=this;let e=null,i=null,n=null;return this.on("error",(t=>{e=t})),this.on("readable",(function(){null!==i&&s(t.read())})),this.on("close",(function(){null!==i&&s(null)})),{[M](){return this},next:()=>new Promise((function(e,r){i=e,n=r;const a=t.read();null!==a?s(a):8&t._duplexState&&s(null)})),return:()=>a(null),throw:t=>a(t)};function s(s){null!==n&&(e?n(e):null!==s||t._duplexState&c?i({value:s,done:null===s}):n(r),n=i=null)}function a(e){return t.destroy(e),new Promise(((i,n)=>{if(8&t._duplexState)return i({value:void 0,done:!0});t.once("close",(function(){e?n(e):i({value:void 0,done:!0})}))}))}}}class $ extends Y{constructor(t){super(t),this._duplexState|=16385,this._writableState=new U(this,t),t&&(t.writev&&(this._writev=t.writev),t.write&&(this._write=t.write),t.final&&(this._final=t.final),t.eagerOpen&&this._writableState.updateNextTick())}cork(){this._duplexState|=P}uncork(){this._duplexState&=I,this._writableState.updateNextTick()}_writev(t,e){e(null)}_write(t,e){this._writableState.autoBatch(t,e)}_final(t){t(null)}static isBackpressured(t){return!!(146800654&t._duplexState)}static drained(t){if(t.destroyed)return Promise.resolve(!1);const e=t._writableState;var i;const n=((i=t)._writev!==$.prototype._writev&&i._writev!==tt.prototype._writev?Math.min(1,e.queue.length):e.queue.length)+(67108864&t._duplexState?1:0);return 0===n?Promise.resolve(!0):(null===e.drains&&(e.drains=[]),new Promise((t=>{e.drains.push({writes:n,resolve:t})})))}write(t){return this._writableState.updateNextTick(),this._writableState.push(t)}end(t){return this._writableState.updateNextTick(),this._writableState.end(t),this}}class tt extends Z{constructor(t){super(t),this._duplexState=1|this._duplexState&p,this._writableState=new U(this,t),t&&(t.writev&&(this._writev=t.writev),t.write&&(this._write=t.write),t.final&&(this._final=t.final))}cork(){this._duplexState|=P}uncork(){this._duplexState&=I,this._writableState.updateNextTick()}_writev(t,e){e(null)}_write(t,e){this._writableState.autoBatch(t,e)}_final(t){t(null)}write(t){return this._writableState.updateNextTick(),this._writableState.push(t)}end(t){return this._writableState.updateNextTick(),this._writableState.end(t),this}}class et extends tt{constructor(t){super(t),this._transformState=new D(this),t&&(t.transform&&(this._transform=t.transform),t.flush&&(this._flush=t.flush))}_write(t,e){this._readableState.buffered>=this._readableState.highWaterMark?this._transformState.data=t:this._transform(t,this._transformState.afterTransform)}_read(t){if(null!==this._transformState.data){const e=this._transformState.data;this._transformState.data=null,t(null),this._transform(e,this._transformState.afterTransform)}else t(null)}destroy(t){super.destroy(t),null!==this._transformState.data&&(this._transformState.data=null,this._transformState.afterTransform())}_transform(t,e){e(null,t)}_flush(t){t(null)}_final(t){this._transformState.afterFinal=t,this._flush(it.bind(this))}}function it(t,e){const i=this._transformState.afterFinal;if(t)return i(t);null!=e&&this.push(e),this.push(null),i(null)}function nt(t,...e){const i=Array.isArray(t)?[...t,...e]:[t,...e],n=i.length&&"function"==typeof i[i.length-1]?i.pop():null;if(i.length<2)throw new Error("Pipeline requires at least 2 streams");let r=i[0],a=null,u=null;for(let t=1;t<i.length;t++)a=i[t],at(r)?r.pipe(a,l):(o(r,!0,t>1,l),r.pipe(a)),r=a;if(n){let t=!1;const e=at(a)||!(!a._writableState||!a._writableState.autoDestroy);a.on("error",(t=>{null===u&&(u=t)})),a.on("finish",(()=>{t=!0,e||n(u)})),e&&a.on("close",(()=>n(u||(t?null:s))))}return a;function o(t,e,i,n){t.on("error",n),t.on("close",(function(){return e&&t._readableState&&!t._readableState.ended||i&&t._writableState&&!t._writableState.ended?n(s):void 0}))}function l(t){if(t&&!u){u=t;for(const e of i)e.destroy(t)}}}function rt(t){return t}function st(t){return!!t._readableState||!!t._writableState}function at(t){return"number"==typeof t._duplexState&&st(t)}function ut(t){return function(t){return"object"==typeof t&&null!==t&&"number"==typeof t.byteLength}(t)?t.byteLength:1024}function ot(){}function lt(){this.destroy(new Error("Stream aborted."))}t.exports={pipeline:nt,pipelinePromise:function(...t){return new Promise(((e,i)=>nt(...t,(t=>{if(t)return i(t);e()}))))},isStream:st,isStreamx:at,isEnded:function(t){return!!t._readableState&&t._readableState.ended},isFinished:function(t){return!!t._writableState&&t._writableState.ended},isDisturbed:function(t){return!!(1&~t._duplexState)||!!(t._duplexState&F)},getStreamError:function(t,e={}){const i=t._readableState&&t._readableState.error||t._writableState&&t._writableState.error;return e.all||i!==r?i:null},Stream:Y,Writable:$,Readable:Z,Duplex:tt,Transform:et,PassThrough:class extends et{}}},6868:(t,e,i)=>{const n=i(6706),r=i(815),s=i(9896),a=i(6928),u="win32"===(global.Bare?.platform||process.platform);function o(){return process.umask?process.umask():0}function l(t,e,i,n){if(e===i)return n(null,!0);t.lstat(e,(function(r,s){return r&&"ENOENT"===r.code?l(t,a.join(e,".."),i,n):r?n(r):void n(null,s.isDirectory())}))}function h(){}function d(t){return t}function c(t){return u?t.replace(/\\/g,"/").replace(/[:?<>|]/g,"_"):t}function f(t,e){return function(i){i.name=i.name.split("/").slice(e).join("/");const n=i.linkname;return n&&("link"===i.type||a.isAbsolute(n))&&(i.linkname=n.split("/").slice(e).join("/")),t(i)}}e.pack=function(t,e){t||(t="."),e||(e={});const i=e.fs||s,u=e.ignore||e.filter||h,l=e.mapStream||d,p=function(t,e,i,n,r,s){r||(r=["."]);const u=r.slice(0);return function(o){if(!u.length)return o(null);const l=u.shift(),h=a.join(i,l);e.call(t,h,(function(e,d){return e?o(-1===r.indexOf(l)&&"ENOENT"===e.code?null:e):d.isDirectory()?void t.readdir(h,(function(t,e){if(t)return o(t);s&&e.sort();for(let t=0;t<e.length;t++)n(a.join(i,l,e[t]))||u.push(a.join(l,e[t]));o(null,l,d)})):o(null,l,d)}))}}(i,e.dereference?i.stat:i.lstat,t,u,e.entries,e.sort),_=!1!==e.strict,m="number"==typeof e.umask?~e.umask:~o(),b=e.pack||n.pack(),S=e.finish||h;let y=e.map||h,g="number"==typeof e.dmode?e.dmode:0,x="number"==typeof e.fmode?e.fmode:0;function w(n,s,u){if(b.destroyed)return;if(n)return b.destroy(n);if(!s)return!1!==e.finalize&&b.finalize(),S(b);if(u.isSocket())return k();let o={name:c(s),mode:(u.mode|(u.isDirectory()?g:x))&m,mtime:u.mtime,size:u.size,type:"file",uid:u.uid,gid:u.gid};if(u.isDirectory())return o.size=0,o.type="directory",o=y(o)||o,b.entry(o,k);if(u.isSymbolicLink())return o.size=0,o.type="symlink",o=y(o)||o,function(e,n){i.readlink(a.join(t,e),(function(t,e){if(t)return b.destroy(t);n.linkname=c(e),b.entry(n,k)}))}(s,o);if(o=y(o)||o,!u.isFile())return _?b.destroy(new Error("unsupported type for "+s)):k();const h=b.entry(o,k),d=l(i.createReadStream(a.join(t,s),{start:0,end:o.size>0?o.size-1:o.size}),o);d.on("error",(function(t){h.destroy(t)})),r(d,h)}function k(t){if(t)return b.destroy(t);p(w)}return e.strip&&(y=f(y,e.strip)),e.readable&&(g|=parseInt(555,8),x|=parseInt(444,8)),e.writable&&(g|=parseInt(333,8),x|=parseInt(222,8)),k(),b},e.extract=function(t,e){t||(t="."),e||(e={}),t=a.resolve(t);const i=e.fs||s,p=e.ignore||e.filter||h,_=e.mapStream||d,m=!1!==e.chown&&!u&&0===(process.getuid?process.getuid():-1),b=e.extract||n.extract(),S=[],y=new Date,g="number"==typeof e.umask?~e.umask:~o(),x=!1!==e.strict;let w=e.map||h,k="number"==typeof e.dmode?e.dmode:0,v="number"==typeof e.fmode?e.fmode:0;return e.strip&&(w=f(w,e.strip)),e.readable&&(k|=parseInt(555,8),v|=parseInt(444,8)),e.writable&&(k|=parseInt(333,8),v|=parseInt(222,8)),b.on("entry",(function(n,s,o){(n=w(n)||n).name=c(n.name);const h=a.join(t,a.join("/",n.name));if(p(h,n))return s.resume(),o();if("directory"===n.type)return S.push([h,n.mtime]),P(h,{fs:i,own:m,uid:n.uid,gid:n.gid,mode:n.mode},f);const d=a.dirname(h);function f(t){if(t)return o(t);!function(t,n,r){!1===e.utimes?r():"directory"===n.type?i.utimes(t,y,n.mtime,r):"symlink"===n.type?L(t,r):i.utimes(t,y,n.mtime,(function(e){if(e)return r(e);L(t,r)}))}(h,n,(function(t){return t?o(t):u?o():void E(h,n,o)}))}function b(){const t=i.createWriteStream(h),e=_(s,n);t.on("error",(function(t){e.destroy(t)})),r(e,t,(function(e){if(e)return o(e);t.on("close",f)}))}l(i,d,a.join(t,"."),(function(r,l){return r?o(r):l?void P(d,{fs:i,own:m,uid:n.uid,gid:n.gid,mode:493},(function(r){if(r)return o(r);switch(n.type){case"file":return b();case"link":return function(){if(u)return o();i.unlink(h,(function(){const r=a.join(t,a.join("/",n.linkname));i.link(r,h,(function(t){if(t&&"EPERM"===t.code&&e.hardlinkAsFilesFallback)return s=i.createReadStream(r),b();f(t)}))}))}();case"symlink":return function(){if(u)return o();i.unlink(h,(function(){if(!a.resolve(a.dirname(h),n.linkname).startsWith(t))return o(new Error(h+" is not a valid symlink"));i.symlink(n.linkname,h,f)}))}()}if(x)return o(new Error("unsupported type for "+h+" ("+n.type+")"));s.resume(),o()})):o(new Error(d+" is not a valid path"))}))})),e.finish&&b.on("finish",e.finish),b;function L(t,e){let n;for(;(n=(r=S).length?r[r.length-1]:null)&&t.slice(0,n[0].length)!==n[0];)S.pop();var r;if(!n)return e();i.utimes(n[0],y,n[1],e)}function E(t,e,n){const r="symlink"===e.type,s=r?i.lchmod:i.chmod,a=r?i.lchown:i.chown;if(!s)return n();const u=(e.mode|("directory"===e.type?k:v))&g;function o(e){return e?n(e):s?void s.call(i,t,u,n):n()}a&&m?a.call(i,t,e.uid,e.gid,o):o(null)}function P(t,e,n){i.stat(t,(function(r){return r?"ENOENT"!==r.code?n(r):void i.mkdir(t,{mode:e.mode,recursive:!0},(function(i,r){if(i)return n(i);E(t,e,n)})):n(null)}))}}},1699:(t,e,i)=>{const n={S_IFMT:61440,S_IFDIR:16384,S_IFCHR:8192,S_IFBLK:24576,S_IFIFO:4096,S_IFLNK:40960};try{t.exports=i(9896).constants||n}catch{t.exports=n}},3807:(t,e,i)=>{const{Writable:n,Readable:r,getStreamError:s}=i(8179),a=i(6080),u=i(7042),o=i(3544),l=u.alloc(0);class h{constructor(){this.buffered=0,this.shifted=0,this.queue=new a,this._offset=0}push(t){this.buffered+=t.byteLength,this.queue.push(t)}shiftFirst(t){return 0===this._buffered?null:this._next(t)}shift(t){if(t>this.buffered)return null;if(0===t)return l;let e=this._next(t);if(t===e.byteLength)return e;const i=[e];for(;(t-=e.byteLength)>0;)e=this._next(t),i.push(e);return u.concat(i)}_next(t){const e=this.queue.peek(),i=e.byteLength-this._offset;if(t>=i){const t=this._offset?e.subarray(this._offset,e.byteLength):e;return this.queue.shift(),this._offset=0,this.buffered-=i,this.shifted+=i,t}return this.buffered-=t,this.shifted+=t,e.subarray(this._offset,this._offset+=t)}}class d extends r{constructor(t,e,i){super(),this.header=e,this.offset=i,this._parent=t}_read(t){0===this.header.size&&this.push(null),this._parent._stream===this&&this._parent._update(),t(null)}_predestroy(){this._parent.destroy(s(this))}_detach(){this._parent._stream===this&&(this._parent._stream=null,this._parent._missing=p(this.header.size),this._parent._update())}_destroy(t){this._detach(),t(null)}}class c extends n{constructor(t){super(t),t||(t={}),this._buffer=new h,this._offset=0,this._header=null,this._stream=null,this._missing=0,this._longHeader=!1,this._callback=f,this._locked=!1,this._finished=!1,this._pax=null,this._paxGlobal=null,this._gnuLongPath=null,this._gnuLongLinkPath=null,this._filenameEncoding=t.filenameEncoding||"utf-8",this._allowUnknownFormat=!!t.allowUnknownFormat,this._unlockBound=this._unlock.bind(this)}_unlock(t){if(this._locked=!1,t)return this.destroy(t),void this._continueWrite(t);this._update()}_consumeHeader(){if(this._locked)return!1;this._offset=this._buffer.shifted;try{this._header=o.decode(this._buffer.shift(512),this._filenameEncoding,this._allowUnknownFormat)}catch(t){return this._continueWrite(t),!1}if(!this._header)return!0;switch(this._header.type){case"gnu-long-path":case"gnu-long-link-path":case"pax-global-header":case"pax-header":return this._longHeader=!0,this._missing=this._header.size,!0}return this._locked=!0,this._applyLongHeaders(),0===this._header.size||"directory"===this._header.type?(this.emit("entry",this._header,this._createStream(),this._unlockBound),!0):(this._stream=this._createStream(),this._missing=this._header.size,this.emit("entry",this._header,this._stream,this._unlockBound),!0)}_applyLongHeaders(){this._gnuLongPath&&(this._header.name=this._gnuLongPath,this._gnuLongPath=null),this._gnuLongLinkPath&&(this._header.linkname=this._gnuLongLinkPath,this._gnuLongLinkPath=null),this._pax&&(this._pax.path&&(this._header.name=this._pax.path),this._pax.linkpath&&(this._header.linkname=this._pax.linkpath),this._pax.size&&(this._header.size=parseInt(this._pax.size,10)),this._header.pax=this._pax,this._pax=null)}_decodeLongHeader(t){switch(this._header.type){case"gnu-long-path":this._gnuLongPath=o.decodeLongPath(t,this._filenameEncoding);break;case"gnu-long-link-path":this._gnuLongLinkPath=o.decodeLongPath(t,this._filenameEncoding);break;case"pax-global-header":this._paxGlobal=o.decodePax(t);break;case"pax-header":this._pax=null===this._paxGlobal?o.decodePax(t):Object.assign({},this._paxGlobal,o.decodePax(t))}}_consumeLongHeader(){this._longHeader=!1,this._missing=p(this._header.size);const t=this._buffer.shift(this._header.size);try{this._decodeLongHeader(t)}catch(t){return this._continueWrite(t),!1}return!0}_consumeStream(){const t=this._buffer.shiftFirst(this._missing);if(null===t)return!1;this._missing-=t.byteLength;const e=this._stream.push(t);return 0===this._missing?(this._stream.push(null),e&&this._stream._detach(),e&&!1===this._locked):e}_createStream(){return new d(this,this._header,this._offset)}_update(){for(;this._buffer.buffered>0&&!this.destroying;)if(this._missing>0){if(null!==this._stream){if(!1===this._consumeStream())return;continue}if(!0===this._longHeader){if(this._missing>this._buffer.buffered)break;if(!1===this._consumeLongHeader())return!1;continue}const t=this._buffer.shiftFirst(this._missing);null!==t&&(this._missing-=t.byteLength)}else{if(this._buffer.buffered<512)break;if(null!==this._stream||!1===this._consumeHeader())return}this._continueWrite(null)}_continueWrite(t){const e=this._callback;this._callback=f,e(t)}_write(t,e){this._callback=e,this._buffer.push(t),this._update()}_final(t){this._finished=0===this._missing&&0===this._buffer.buffered,t(this._finished?null:new Error("Unexpected end of data"))}_predestroy(){this._continueWrite(null)}_destroy(t){this._stream&&this._stream.destroy(s(this)),t(null)}[Symbol.asyncIterator](){let t=null,e=null,i=null,n=null,r=null;const s=this;return this.on("entry",(function(t,s,a){r=a,s.on("error",f),e?(e({value:s,done:!1}),e=i=null):n=s})),this.on("error",(e=>{t=e})),this.on("close",(function(){a(t),e&&(t?i(t):e({value:void 0,done:!0}),e=i=null)})),{[Symbol.asyncIterator](){return this},next:()=>new Promise(u),return:()=>o(null),throw:t=>o(t)};function a(t){if(!r)return;const e=r;r=null,e(t)}function u(r,u){return t?u(t):n?(r({value:n,done:!1}),void(n=null)):(e=r,i=u,a(null),void(s._finished&&e&&(e({value:void 0,done:!0}),e=i=null)))}function o(t){return s.destroy(t),a(t),new Promise(((e,i)=>{if(s.destroyed)return e({value:void 0,done:!0});s.once("close",(function(){t?i(t):e({value:void 0,done:!0})}))}))}}}function f(){}function p(t){return(t&=511)&&512-t}t.exports=function(t){return new c(t)}},3544:(t,e,i)=>{const n=i(7042),r="0".charCodeAt(0),s=n.from([117,115,116,97,114,0]),a=n.from([r,r]),u=n.from([117,115,116,97,114,32]),o=n.from([32,0]);function l(t,e,i,n){for(;i<n;i++)if(t[i]===e)return i;return n}function h(t){let e=256;for(let i=0;i<148;i++)e+=t[i];for(let i=156;i<512;i++)e+=t[i];return e}function d(t,e){return(t=t.toString(8)).length>e?"7777777777777777777".slice(0,e)+" ":"0000000000000000000".slice(0,e-t.length)+t+" "}function c(t,e,i){if(128&(t=t.subarray(e,e+i))[e=0])return function(t){let e;if(128===t[0])e=!0;else{if(255!==t[0])return null;e=!1}const i=[];let n;for(n=t.length-1;n>0;n--){const r=t[n];e?i.push(r):i.push(255-r)}let r=0;const s=i.length;for(n=0;n<s;n++)r+=i[n]*Math.pow(256,n);return e?r:-1*r}(t);{for(;e<t.length&&32===t[e];)e++;const i=(r=l(t,32,e,t.length),s=t.length,a=t.length,"number"!=typeof r?a:(r=~~r)>=s?s:r>=0||(r+=s)>=0?r:0);for(;e<i&&0===t[e];)e++;return i===e?0:parseInt(n.toString(t.subarray(e,i)),8)}var r,s,a}function f(t,e,i,r){return n.toString(t.subarray(e,l(t,0,e,e+i)),r)}function p(t){const e=n.byteLength(t);let i=Math.floor(Math.log(e)/Math.log(10))+1;return e+i>=Math.pow(10,i)&&i++,e+i+t}e.decodeLongPath=function(t,e){return f(t,0,t.length,e)},e.encodePax=function(t){let e="";t.name&&(e+=p(" path="+t.name+"\n")),t.linkname&&(e+=p(" linkpath="+t.linkname+"\n"));const i=t.pax;if(i)for(const t in i)e+=p(" "+t+"="+i[t]+"\n");return n.from(e)},e.decodePax=function(t){const e={};for(;t.length;){let i=0;for(;i<t.length&&32!==t[i];)i++;const r=parseInt(n.toString(t.subarray(0,i)),10);if(!r)return e;const s=n.toString(t.subarray(i+1,r-1)),a=s.indexOf("=");if(-1===a)return e;e[s.slice(0,a)]=s.slice(a+1),t=t.subarray(r)}return e},e.encode=function(t){const e=n.alloc(512);let i=t.name,u="";if(5===t.typeflag&&"/"!==i[i.length-1]&&(i+="/"),n.byteLength(i)!==i.length)return null;for(;n.byteLength(i)>100;){const t=i.indexOf("/");if(-1===t)return null;u+=u?"/"+i.slice(0,t):i.slice(0,t),i=i.slice(t+1)}return n.byteLength(i)>100||n.byteLength(u)>155||t.linkname&&n.byteLength(t.linkname)>100?null:(n.write(e,i),n.write(e,d(4095&t.mode,6),100),n.write(e,d(t.uid,6),108),n.write(e,d(t.gid,6),116),function(t,e,i){t.toString(8).length>11?function(t,e,i){e[124]=128;for(let i=11;i>0;i--)e[124+i]=255&t,t=Math.floor(t/256)}(t,e):n.write(e,d(t,11),124)}(t.size,e),n.write(e,d(t.mtime.getTime()/1e3|0,11),136),e[156]=r+function(t){switch(t){case"file":return 0;case"link":return 1;case"symlink":return 2;case"character-device":return 3;case"block-device":return 4;case"directory":return 5;case"fifo":return 6;case"contiguous-file":return 7;case"pax-header":return 72}return 0}(t.type),t.linkname&&n.write(e,t.linkname,157),n.copy(s,e,257),n.copy(a,e,263),t.uname&&n.write(e,t.uname,265),t.gname&&n.write(e,t.gname,297),n.write(e,d(t.devmajor||0,6),329),n.write(e,d(t.devminor||0,6),337),u&&n.write(e,u,345),n.write(e,d(h(e),6),148),e)},e.decode=function(t,e,i){let a=0===t[156]?0:t[156]-r,l=f(t,0,100,e);const d=c(t,100,8),p=c(t,108,8),_=c(t,116,8),m=c(t,124,12),b=c(t,136,12),S=function(t){switch(t){case 0:return"file";case 1:return"link";case 2:return"symlink";case 3:return"character-device";case 4:return"block-device";case 5:return"directory";case 6:return"fifo";case 7:return"contiguous-file";case 72:return"pax-header";case 55:return"pax-global-header";case 27:return"gnu-long-link-path";case 28:case 30:return"gnu-long-path"}return null}(a),y=0===t[157]?null:f(t,157,100,e),g=f(t,265,32),x=f(t,297,32),w=c(t,329,8),k=c(t,337,8),v=h(t);if(256===v)return null;if(v!==c(t,148,8))throw new Error("Invalid tar header. Maybe the tar is corrupted or it needs to be gunzipped?");if(function(t){return n.equals(s,t.subarray(257,263))}(t))t[345]&&(l=f(t,345,155,e)+"/"+l);else if(function(t){return n.equals(u,t.subarray(257,263))&&n.equals(o,t.subarray(263,265))}(t));else if(!i)throw new Error("Invalid tar header: unknown format.");return 0===a&&l&&"/"===l[l.length-1]&&(a=5),{name:l,mode:d,uid:p,gid:_,size:m,mtime:new Date(1e3*b),type:S,linkname:y,uname:g,gname:x,devmajor:w,devminor:k,pax:null}}},6706:(t,e,i)=>{e.extract=i(3807),e.pack=i(4009)},4009:(t,e,i)=>{const{Readable:n,Writable:r,getStreamError:s}=i(8179),a=i(7042),u=i(1699),o=i(3544),l=a.alloc(1024);class h extends r{constructor(t,e,i){super({mapWritable:p,eagerOpen:!0}),this.written=0,this.header=e,this._callback=i,this._linkname=null,this._isLinkname="symlink"===e.type&&!e.linkname,this._isVoid="file"!==e.type&&"contiguous-file"!==e.type,this._finished=!1,this._pack=t,this._openCallback=null,null===this._pack._stream?this._pack._stream=this:this._pack._pending.push(this)}_open(t){this._openCallback=t,this._pack._stream===this&&this._continueOpen()}_continuePack(t){if(null===this._callback)return;const e=this._callback;this._callback=null,e(t)}_continueOpen(){null===this._pack._stream&&(this._pack._stream=this);const t=this._openCallback;if(this._openCallback=null,null!==t){if(this._pack.destroying)return t(new Error("pack stream destroyed"));if(this._pack._finalized)return t(new Error("pack stream is already finalized"));this._pack._stream=this,this._isLinkname||this._pack._encode(this.header),this._isVoid&&(this._finish(),this._continuePack(null)),t(null)}}_write(t,e){return this._isLinkname?(this._linkname=this._linkname?a.concat([this._linkname,t]):t,e(null)):this._isVoid?t.byteLength>0?e(new Error("No body allowed for this entry")):e():(this.written+=t.byteLength,this._pack.push(t)?e():void(this._pack._drain=e))}_finish(){this._finished||(this._finished=!0,this._isLinkname&&(this.header.linkname=this._linkname?a.toString(this._linkname,"utf-8"):"",this._pack._encode(this.header)),f(this._pack,this.header.size),this._pack._done(this))}_final(t){if(this.written!==this.header.size)return t(new Error("Size mismatch"));this._finish(),t(null)}_getError(){return s(this)||new Error("tar entry destroyed")}_predestroy(){this._pack.destroy(this._getError())}_destroy(t){this._pack._done(this),this._continuePack(this._finished?null:this._getError()),t()}}class d extends n{constructor(t){super(t),this._drain=c,this._finalized=!1,this._finalizing=!1,this._pending=[],this._stream=null}entry(t,e,i){if(this._finalized||this.destroying)throw new Error("already finalized or destroyed");"function"==typeof e&&(i=e,e=null),i||(i=c),t.size&&"symlink"!==t.type||(t.size=0),t.type||(t.type=function(t){switch(t&u.S_IFMT){case u.S_IFBLK:return"block-device";case u.S_IFCHR:return"character-device";case u.S_IFDIR:return"directory";case u.S_IFIFO:return"fifo";case u.S_IFLNK:return"symlink"}return"file"}(t.mode)),t.mode||(t.mode="directory"===t.type?493:420),t.uid||(t.uid=0),t.gid||(t.gid=0),t.mtime||(t.mtime=new Date),"string"==typeof e&&(e=a.from(e));const n=new h(this,t,i);return a.isBuffer(e)?(t.size=e.byteLength,n.write(e),n.end(),n):(n._isVoid,n)}finalize(){this._stream||this._pending.length>0?this._finalizing=!0:this._finalized||(this._finalized=!0,this.push(l),this.push(null))}_done(t){t===this._stream&&(this._stream=null,this._finalizing&&this.finalize(),this._pending.length&&this._pending.shift()._continueOpen())}_encode(t){if(!t.pax){const e=o.encode(t);if(e)return void this.push(e)}this._encodePax(t)}_encodePax(t){const e=o.encodePax({name:t.name,linkname:t.linkname,pax:t.pax}),i={name:"PaxHeader",mode:t.mode,uid:t.uid,gid:t.gid,size:e.byteLength,mtime:t.mtime,type:"pax-header",linkname:t.linkname&&"PaxHeader",uname:t.uname,gname:t.gname,devmajor:t.devmajor,devminor:t.devminor};this.push(o.encode(i)),this.push(e),f(this,e.byteLength),i.size=t.size,i.type=t.type,this.push(o.encode(i))}_doDrain(){const t=this._drain;this._drain=c,t()}_predestroy(){const t=s(this);for(this._stream&&this._stream.destroy(t);this._pending.length;){const e=this._pending.shift();e.destroy(t),e._continueOpen()}this._doDrain()}_read(t){this._doDrain(),t()}}function c(){}function f(t,e){(e&=511)&&t.push(l.subarray(0,512-e))}function p(t){return a.isBuffer(t)?t:a.from(t)}t.exports=function(t){return new d(t)}},5335:(t,e,i)=>{const n=i(4611),r=i(8426);t.exports=class{constructor(t="utf8"){switch(this.encoding=function(t){switch(t=t.toLowerCase()){case"utf8":case"utf-8":return"utf8";case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return"utf16le";case"latin1":case"binary":return"latin1";case"base64":case"ascii":case"hex":return t;default:throw new Error("Unknown encoding: "+t)}}(t),this.encoding){case"utf8":this.decoder=new r;break;case"utf16le":case"base64":throw new Error("Unsupported encoding: "+this.encoding);default:this.decoder=new n(this.encoding)}}get remaining(){return this.decoder.remaining}push(t){return"string"==typeof t?t:this.decoder.decode(t)}write(t){return this.push(t)}end(t){let e="";return t&&(e=this.push(t)),e+=this.decoder.flush(),e}}},4611:(t,e,i)=>{const n=i(7042);t.exports=class{constructor(t){this.encoding=t}get remaining(){return 0}decode(t){return n.toString(t,this.encoding)}flush(){return""}}},8426:(t,e,i)=>{const n=i(7042);t.exports=class{constructor(){this.codePoint=0,this.bytesSeen=0,this.bytesNeeded=0,this.lowerBoundary=128,this.upperBoundary=191}get remaining(){return this.bytesSeen}decode(t){if(0===this.bytesNeeded){let e=!0;for(let i=Math.max(0,t.byteLength-4),n=t.byteLength;i<n&&e;i++)e=t[i]<=127;if(e)return n.toString(t,"utf8")}let e="";for(let i=0,n=t.byteLength;i<n;i++){const n=t[i];0!==this.bytesNeeded?n<this.lowerBoundary||n>this.upperBoundary?(this.codePoint=0,this.bytesNeeded=0,this.bytesSeen=0,this.lowerBoundary=128,this.upperBoundary=191,e+="�"):(this.lowerBoundary=128,this.upperBoundary=191,this.codePoint=this.codePoint<<6|63&n,this.bytesSeen++,this.bytesSeen===this.bytesNeeded&&(e+=String.fromCodePoint(this.codePoint),this.codePoint=0,this.bytesNeeded=0,this.bytesSeen=0)):n<=127?e+=String.fromCharCode(n):(this.bytesSeen=1,n>=194&&n<=223?(this.bytesNeeded=2,this.codePoint=31&n):n>=224&&n<=239?(224===n?this.lowerBoundary=160:237===n&&(this.upperBoundary=159),this.bytesNeeded=3,this.codePoint=15&n):n>=240&&n<=244?(240===n&&(this.lowerBoundary=144),244===n&&(this.upperBoundary=143),this.bytesNeeded=4,this.codePoint=7&n):e+="�")}return e}flush(){const t=this.bytesNeeded>0?"�":"";return this.codePoint=0,this.bytesNeeded=0,this.bytesSeen=0,this.lowerBoundary=128,this.upperBoundary=191,t}}}};
//# sourceMappingURL=https://cursor-sourcemaps.s3.amazonaws.com/sourcemaps/b6fb41b5f36bda05cab7109606e7404a65d1ff30/extensions/cursor-retrieval/dist/868.main.js.map
//# debugId=d33b210a-387a-5b77-8b6e-5593aeffdaac
