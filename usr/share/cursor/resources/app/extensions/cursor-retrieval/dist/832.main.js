
!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},n=(new e.Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="3665b3c4-88ef-5b36-af86-68271592678e")}catch(e){}}();
exports.id=832,exports.ids=[832],exports.modules={4025:(e,t,r)=>{var n=r(181).Buffer,i=[0,1996959894,3993919788,2567524794,124634137,1886057615,3915621685,2657392035,249268274,2044508324,3772115230,2547177864,162941995,2125561021,3887607047,2428444049,498536548,1789927666,4089016648,2227061214,450548861,1843258603,4107580753,2211677639,325883990,1684777152,4251122042,2321926636,335633487,1661365465,4195302755,2366115317,997073096,1281953886,3579855332,2724688242,1006888145,1258607687,3524101629,2768942443,901097722,1119000684,3686517206,2898065728,853044451,1172266101,3705015759,2882616665,651767980,1373503546,3369554304,3218104598,565507253,1454621731,3485111705,3099436303,671266974,1594198024,3322730930,2970347812,795835527,1483230225,3244367275,3060149565,1994146192,31158534,2563907772,4023717930,1907459465,112637215,2680153253,3904427059,2013776290,251722036,2517215374,3775830040,2137656763,141376813,2439277719,3865271297,1802195444,476864866,2238001368,4066508878,1812370925,453092731,2181625025,4111451223,1706088902,314042704,2344532202,4240017532,1658658271,366619977,2362670323,4224994405,1303535960,984961486,2747007092,3569037538,1256170817,1037604311,2765210733,3554079995,1131014506,879679996,2909243462,3663771856,1141124467,855842277,2852801631,3708648649,1342533948,654459306,3188396048,3373015174,1466479909,544179635,3110523913,3462522015,1591671054,702138776,2966460450,3352799412,1504918807,783551873,3082640443,3233442989,3988292384,2596254646,62317068,1957810842,3939845945,2647816111,81470997,1943803523,3814918930,2489596804,225274430,2053790376,3826175755,2466906013,167816743,2097651377,4027552580,2265490386,503444072,1762050814,4150417245,2154129355,426522225,1852507879,4275313526,2312317920,282753626,1742555852,4189708143,2394877945,397917763,1622183637,3604390888,2714866558,953729732,1340076626,3518719985,2797360999,1068828381,1219638859,3624741850,2936675148,906185462,1090812512,3747672003,2825379669,829329135,1181335161,3412177804,3160834842,628085408,1382605366,3423369109,3138078467,570562233,1426400815,3317316542,2998733608,733239954,1555261956,3268935591,3050360625,752459403,1541320221,2607071920,3965973030,1969922972,40735498,2617837225,3943577151,1913087877,83908371,2512341634,3803740692,2075208622,213261112,2463272603,3855990285,2094854071,198958881,2262029012,4057260610,1759359992,534414190,2176718541,4139329115,1873836001,414664567,2282248934,4279200368,1711684554,285281116,2405801727,4167216745,1634467795,376229701,2685067896,3608007406,1308918612,956543938,2808555105,3495958263,1231636301,1047427035,2932959818,3654703836,1088359270,936918e3,2847714899,3736837829,1202900863,817233897,3183342108,3401237130,1404277552,615818150,3134207493,3453421203,1423857449,601450431,3009837614,3294710456,1567103746,711928724,3020668471,3272380065,1510334235,755167117];function o(e){if(n.isBuffer(e))return e;var t="function"==typeof n.alloc&&"function"==typeof n.from;if("number"==typeof e)return t?n.alloc(e):new n(e);if("string"==typeof e)return t?n.from(e):new n(e);throw new Error("input must be buffer, number, or string, received "+typeof e)}function s(e,t){e=o(e),n.isBuffer(t)&&(t=t.readUInt32BE(0));for(var r=~t,s=0;s<e.length;s++)r=i[255&(r^e[s])]^r>>>8;return~r}function a(){return e=s.apply(null,arguments),(t=o(4)).writeInt32BE(e,0),t;var e,t}"undefined"!=typeof Int32Array&&(i=new Int32Array(i)),a.signed=function(){return s.apply(null,arguments)},a.unsigned=function(){return s.apply(null,arguments)>>>0},e.exports=a},8992:(e,t,r)=>{var n=r(3519),i=function(){},o=function(e,t,r){if("function"==typeof t)return o(e,null,t);t||(t={}),r=n(r||i);var s=e._writableState,a=e._readableState,f=t.readable||!1!==t.readable&&e.readable,d=t.writable||!1!==t.writable&&e.writable,l=!1,u=function(){e.writable||c()},c=function(){d=!1,f||r.call(e)},p=function(){f=!1,d||r.call(e)},h=function(t){r.call(e,t?new Error("exited with error code: "+t):null)},m=function(t){r.call(e,t)},y=function(){process.nextTick(E)},E=function(){if(!l)return(!f||a&&a.ended&&!a.destroyed)&&(!d||s&&s.ended&&!s.destroyed)?void 0:r.call(e,new Error("premature close"))},g=function(){e.req.on("finish",c)};return function(e){return e.setHeader&&"function"==typeof e.abort}(e)?(e.on("complete",c),e.on("abort",y),e.req?g():e.on("request",g)):d&&!s&&(e.on("end",u),e.on("close",u)),function(e){return e.stdio&&Array.isArray(e.stdio)&&3===e.stdio.length}(e)&&e.on("exit",h),e.on("end",p),e.on("finish",c),!1!==t.error&&e.on("error",m),e.on("close",y),function(){l=!0,e.removeListener("complete",c),e.removeListener("abort",y),e.removeListener("request",g),e.req&&e.req.removeListener("finish",c),e.removeListener("end",u),e.removeListener("close",u),e.removeListener("finish",c),e.removeListener("exit",h),e.removeListener("end",p),e.removeListener("error",m),e.removeListener("close",y)}};e.exports=o},3832:(e,t,r)=>{const n=r(5753)("extract-zip"),{createWriteStream:i,promises:o}=r(9896),s=r(1635),a=r(6928),{promisify:f}=r(9023),d=r(2203),l=r(4824),u=f(l.open),c=f(d.pipeline);class p{constructor(e,t){this.zipPath=e,this.opts=t}async extract(){return n("opening",this.zipPath,"with opts",this.opts),this.zipfile=await u(this.zipPath,{lazyEntries:!0}),this.canceled=!1,new Promise(((e,t)=>{this.zipfile.on("error",(e=>{this.canceled=!0,t(e)})),this.zipfile.readEntry(),this.zipfile.on("close",(()=>{this.canceled||(n("zip extraction complete"),e())})),this.zipfile.on("entry",(async e=>{if(this.canceled)return void n("skipping entry",e.fileName,{cancelled:this.canceled});if(n("zipfile entry",e.fileName),e.fileName.startsWith("__MACOSX/"))return void this.zipfile.readEntry();const r=a.dirname(a.join(this.opts.dir,e.fileName));try{await o.mkdir(r,{recursive:!0});const t=await o.realpath(r);if(a.relative(this.opts.dir,t).split(a.sep).includes(".."))throw new Error(`Out of bound path "${t}" found while processing file ${e.fileName}`);await this.extractEntry(e),n("finished processing",e.fileName),this.zipfile.readEntry()}catch(e){this.canceled=!0,this.zipfile.close(),t(e)}}))}))}async extractEntry(e){if(this.canceled)return void n("skipping entry extraction",e.fileName,{cancelled:this.canceled});this.opts.onEntry&&this.opts.onEntry(e,this.zipfile);const t=a.join(this.opts.dir,e.fileName),r=e.externalFileAttributes>>16&65535,d=40960==(61440&r);let l=16384==(61440&r);!l&&e.fileName.endsWith("/")&&(l=!0);const u=e.versionMadeBy>>8;l||(l=0===u&&16===e.externalFileAttributes),n("extracting entry",{filename:e.fileName,isDir:l,isSymlink:d});const p=511&this.getExtractedMode(r,l),h=l?t:a.dirname(t),m={recursive:!0};if(l&&(m.mode=p),n("mkdir",{dir:h,...m}),await o.mkdir(h,m),l)return;n("opening read stream",t);const y=await f(this.zipfile.openReadStream.bind(this.zipfile))(e);if(d){const e=await s(y);n("creating symlink",e,t),await o.symlink(e,t)}else await c(y,i(t,{mode:p}))}getExtractedMode(e,t){let r=e;return 0===r&&(t?(this.opts.defaultDirMode&&(r=parseInt(this.opts.defaultDirMode,10)),r||(r=493)):(this.opts.defaultFileMode&&(r=parseInt(this.opts.defaultFileMode,10)),r||(r=420))),r}}e.exports=async function(e,t){if(n("creating target directory",t.dir),!a.isAbsolute(t.dir))throw new Error("Target directory is expected to be absolute");return await o.mkdir(t.dir,{recursive:!0}),t.dir=await o.realpath(t.dir),new p(e,t).extract()}},6350:(e,t,r)=>{"use strict";const{PassThrough:n}=r(2203);e.exports=e=>{e={...e};const{array:t}=e;let{encoding:r}=e;const i="buffer"===r;let o=!1;t?o=!(r||i):r=r||"utf8",i&&(r=null);const s=new n({objectMode:o});r&&s.setEncoding(r);let a=0;const f=[];return s.on("data",(e=>{f.push(e),o?a=f.length:a+=e.length})),s.getBufferedValue=()=>t?f:i?Buffer.concat(f,a):f.join(""),s.getBufferedLength=()=>a,s}},1635:(e,t,r)=>{"use strict";const{constants:n}=r(181),i=r(815),o=r(6350);class s extends Error{constructor(){super("maxBuffer exceeded"),this.name="MaxBufferError"}}async function a(e,t){if(!e)return Promise.reject(new Error("Expected a stream"));t={maxBuffer:1/0,...t};const{maxBuffer:r}=t;let a;return await new Promise(((f,d)=>{const l=e=>{e&&a.getBufferedLength()<=n.MAX_LENGTH&&(e.bufferedData=a.getBufferedValue()),d(e)};a=i(e,o(t),(e=>{e?l(e):f()})),a.on("data",(()=>{a.getBufferedLength()>r&&l(new s)}))})),a.getBufferedValue()}e.exports=a,e.exports.default=a,e.exports.buffer=(e,t)=>a(e,{...t,encoding:"buffer"}),e.exports.array=(e,t)=>a(e,{...t,array:!0}),e.exports.MaxBufferError=s},3070:(e,t,r)=>{var n=r(9896),i=r(9023),o=r(2203),s=o.Readable,a=o.Writable,f=o.PassThrough,d=r(1436),l=r(4434).EventEmitter;function u(e,t){t=t||{},l.call(this),this.fd=e,this.pend=new d,this.pend.max=1,this.refCount=0,this.autoClose=!!t.autoClose}function c(e,t){t=t||{},s.call(this,t),this.context=e,this.context.ref(),this.start=t.start||0,this.endOffset=t.end,this.pos=this.start,this.destroyed=!1}function p(e,t){t=t||{},a.call(this,t),this.context=e,this.context.ref(),this.start=t.start||0,this.endOffset=null==t.end?1/0:+t.end,this.bytesWritten=0,this.pos=this.start,this.destroyed=!1,this.on("finish",this.destroy.bind(this))}function h(e,t){l.call(this),t=t||{},this.refCount=0,this.buffer=e,this.maxChunkSize=t.maxChunkSize||Number.MAX_SAFE_INTEGER}t.createFromBuffer=function(e,t){return new h(e,t)},t.createFromFd=function(e,t){return new u(e,t)},t.BufferSlicer=h,t.FdSlicer=u,i.inherits(u,l),u.prototype.read=function(e,t,r,i,o){var s=this;s.pend.go((function(a){n.read(s.fd,e,t,r,i,(function(e,t,r){a(),o(e,t,r)}))}))},u.prototype.write=function(e,t,r,i,o){var s=this;s.pend.go((function(a){n.write(s.fd,e,t,r,i,(function(e,t,r){a(),o(e,t,r)}))}))},u.prototype.createReadStream=function(e){return new c(this,e)},u.prototype.createWriteStream=function(e){return new p(this,e)},u.prototype.ref=function(){this.refCount+=1},u.prototype.unref=function(){var e=this;if(e.refCount-=1,!(e.refCount>0)){if(e.refCount<0)throw new Error("invalid unref");e.autoClose&&n.close(e.fd,(function(t){t?e.emit("error",t):e.emit("close")}))}},i.inherits(c,s),c.prototype._read=function(e){var t=this;if(!t.destroyed){var r=Math.min(t._readableState.highWaterMark,e);if(null!=t.endOffset&&(r=Math.min(r,t.endOffset-t.pos)),r<=0)return t.destroyed=!0,t.push(null),void t.context.unref();t.context.pend.go((function(e){if(t.destroyed)return e();var i=new Buffer(r);n.read(t.context.fd,i,0,r,t.pos,(function(r,n){r?t.destroy(r):0===n?(t.destroyed=!0,t.push(null),t.context.unref()):(t.pos+=n,t.push(i.slice(0,n))),e()}))}))}},c.prototype.destroy=function(e){this.destroyed||(e=e||new Error("stream destroyed"),this.destroyed=!0,this.emit("error",e),this.context.unref())},i.inherits(p,a),p.prototype._write=function(e,t,r){var i=this;if(!i.destroyed){if(i.pos+e.length>i.endOffset){var o=new Error("maximum file length exceeded");return o.code="ETOOBIG",i.destroy(),void r(o)}i.context.pend.go((function(t){if(i.destroyed)return t();n.write(i.context.fd,e,0,e.length,i.pos,(function(e,n){e?(i.destroy(),t(),r(e)):(i.bytesWritten+=n,i.pos+=n,i.emit("progress"),t(),r())}))}))}},p.prototype.destroy=function(){this.destroyed||(this.destroyed=!0,this.context.unref())},i.inherits(h,l),h.prototype.read=function(e,t,r,n,i){var o=n+r,s=o-this.buffer.length,a=s>0?s:r;this.buffer.copy(e,t,n,o),setImmediate((function(){i(null,a)}))},h.prototype.write=function(e,t,r,n,i){e.copy(this.buffer,n,t,t+r),setImmediate((function(){i(null,r,e)}))},h.prototype.createReadStream=function(e){var t=new f(e=e||{});t.destroyed=!1,t.start=e.start||0,t.endOffset=e.end,t.pos=t.endOffset||this.buffer.length;for(var r=this.buffer.slice(t.start,t.pos),n=0;;){var i=n+this.maxChunkSize;if(i>=r.length){n<r.length&&t.write(r.slice(n,r.length));break}t.write(r.slice(n,i)),n=i}return t.end(),t.destroy=function(){t.destroyed=!0},t},h.prototype.createWriteStream=function(e){var t=this,r=new a(e=e||{});return r.start=e.start||0,r.endOffset=null==e.end?this.buffer.length:+e.end,r.bytesWritten=0,r.pos=r.start,r.destroyed=!1,r._write=function(e,n,i){if(!r.destroyed){var o=r.pos+e.length;if(o>r.endOffset){var s=new Error("maximum file length exceeded");return s.code="ETOOBIG",r.destroyed=!0,void i(s)}e.copy(t.buffer,r.pos,0,e.length),r.bytesWritten+=e.length,r.pos=o,r.emit("progress"),i()}},r.destroy=function(){r.destroyed=!0},r},h.prototype.ref=function(){this.refCount+=1},h.prototype.unref=function(){if(this.refCount-=1,this.refCount<0)throw new Error("invalid unref")}},1436:e=>{function t(){this.pending=0,this.max=1/0,this.listeners=[],this.waiting=[],this.error=null}function r(e){e.pending+=1;var t=!1;return function(i){if(t)throw new Error("callback called twice");if(t=!0,e.error=e.error||i,e.pending-=1,e.waiting.length>0&&e.pending<e.max)n(e,e.waiting.shift());else if(0===e.pending){var o=e.listeners;e.listeners=[],o.forEach(r)}};function r(t){t(e.error)}}function n(e,t){t(r(e))}e.exports=t,t.prototype.go=function(e){this.pending<this.max?n(this,e):this.waiting.push(e)},t.prototype.wait=function(e){0===this.pending?e(this.error):this.listeners.push(e)},t.prototype.hold=function(){return r(this)}},815:(e,t,r)=>{var n,i=r(3519),o=r(8992);try{n=r(9896)}catch(e){}var s=function(){},a=/^v?\.0/.test(process.version),f=function(e){return"function"==typeof e},d=function(e){e()},l=function(e,t){return e.pipe(t)};e.exports=function(){var e,t=Array.prototype.slice.call(arguments),r=f(t[t.length-1]||s)&&t.pop()||s;if(Array.isArray(t[0])&&(t=t[0]),t.length<2)throw new Error("pump requires two streams per minimum");var u=t.map((function(l,c){var p=c<t.length-1;return function(e,t,r,d){d=i(d);var l=!1;e.on("close",(function(){l=!0})),o(e,{readable:t,writable:r},(function(e){if(e)return d(e);l=!0,d()}));var u=!1;return function(t){if(!l&&!u)return u=!0,function(e){return!!a&&!!n&&(e instanceof(n.ReadStream||s)||e instanceof(n.WriteStream||s))&&f(e.close)}(e)?e.close(s):function(e){return e.setHeader&&f(e.abort)}(e)?e.abort():f(e.destroy)?e.destroy():void d(t||new Error("stream was destroyed"))}}(l,p,c>0,(function(t){e||(e=t),t&&u.forEach(d),p||(u.forEach(d),r(e))}))}));return t.reduce(l)}},4824:(e,t,r)=>{var n=r(9896),i=r(3106),o=r(3070),s=r(4025),a=r(9023),f=r(4434).EventEmitter,d=r(2203).Transform,l=r(2203).PassThrough,u=r(2203).Writable;function c(e,t,r){"function"==typeof t&&(r=t,t=null),null==t&&(t={}),null==t.autoClose&&(t.autoClose=!1),null==t.lazyEntries&&(t.lazyEntries=!1),null==t.decodeStrings&&(t.decodeStrings=!0),null==t.validateEntrySizes&&(t.validateEntrySizes=!0),null==t.strictFileNames&&(t.strictFileNames=!1),null==r&&(r=F),n.fstat(e,(function(n,i){if(n)return r(n);p(o.createFromFd(e,{autoClose:!0}),i.size,t,r)}))}function p(e,t,r,n){"function"==typeof r&&(n=r,r=null),null==r&&(r={}),null==r.autoClose&&(r.autoClose=!0),null==r.lazyEntries&&(r.lazyEntries=!1),null==r.decodeStrings&&(r.decodeStrings=!0);var i=!!r.decodeStrings;if(null==r.validateEntrySizes&&(r.validateEntrySizes=!0),null==r.strictFileNames&&(r.strictFileNames=!1),null==n&&(n=F),"number"!=typeof t)throw new Error("expected totalSize parameter to be a number");if(t>Number.MAX_SAFE_INTEGER)throw new Error("zip file too large. only file sizes up to 2^52 are supported due to JavaScript's Number type being an IEEE 754 double.");e.ref();var o=Math.min(65557,t),s=b(o),a=t-s.length;v(e,s,0,o,a,(function(f){if(f)return n(f);for(var d=o-22;d>=0;d-=1)if(101010256===s.readUInt32LE(d)){var l=s.slice(d),u=l.readUInt16LE(4);if(0!==u)return n(new Error("multi-disk zip files are not supported: found disk number: "+u));var c=l.readUInt16LE(10),p=l.readUInt32LE(16),m=l.readUInt16LE(20),y=l.length-22;if(m!==y)return n(new Error("invalid comment length. expected: "+y+". found: "+m));var E=i?C(l,22,l.length,!1):l.slice(22);if(65535!==c&&4294967295!==p)return n(null,new h(e,p,t,c,E,r.autoClose,r.lazyEntries,i,r.validateEntrySizes,r.strictFileNames));var g=b(20),w=a+d-g.length;return void v(e,g,0,g.length,w,(function(o){if(o)return n(o);if(117853008!==g.readUInt32LE(0))return n(new Error("invalid zip64 end of central directory locator signature"));var s=I(g,8),a=b(56);v(e,a,0,a.length,s,(function(o){return o?n(o):101075792!==a.readUInt32LE(0)?n(new Error("invalid zip64 end of central directory record signature")):(c=I(a,32),p=I(a,48),n(null,new h(e,p,t,c,E,r.autoClose,r.lazyEntries,i,r.validateEntrySizes,r.strictFileNames)))}))}))}n(new Error("end of central directory record signature not found"))}))}function h(e,t,r,n,i,o,s,a,d,l){var u=this;f.call(u),u.reader=e,u.reader.on("error",(function(e){y(u,e)})),u.reader.once("close",(function(){u.emit("close")})),u.readEntryCursor=t,u.fileSize=r,u.entryCount=n,u.comment=i,u.entriesRead=0,u.autoClose=!!o,u.lazyEntries=!!s,u.decodeStrings=!!a,u.validateEntrySizes=!!d,u.strictFileNames=!!l,u.isOpen=!0,u.emittedError=!1,u.lazyEntries||u._readEntry()}function m(e,t){e.autoClose&&e.close(),y(e,t)}function y(e,t){e.emittedError||(e.emittedError=!0,e.emit("error",t))}function E(){}function g(e,t){return new Date(1980+(e>>9&127),(e>>5&15)-1,31&e,t>>11&31,t>>5&63,2*(31&t),0)}function w(e){return-1!==e.indexOf("\\")?"invalid characters in fileName: "+e:/^[a-zA-Z]:/.test(e)||/^\//.test(e)?"absolute path: "+e:-1!==e.split("/").indexOf("..")?"invalid relative path: "+e:null}function v(e,t,r,n,i,o){if(0===n)return setImmediate((function(){o(null,b(0))}));e.read(t,r,n,i,(function(e,t){return e?o(e):t<n?o(new Error("unexpected EOF")):void o()}))}function x(e){d.call(this),this.actualByteCount=0,this.expectedByteCount=e}function z(){f.call(this),this.refCount=0}function S(e){l.call(this),this.context=e,this.context.ref(),this.unreffedYet=!1}t.open=function(e,t,r){"function"==typeof t&&(r=t,t=null),null==t&&(t={}),null==t.autoClose&&(t.autoClose=!0),null==t.lazyEntries&&(t.lazyEntries=!1),null==t.decodeStrings&&(t.decodeStrings=!0),null==t.validateEntrySizes&&(t.validateEntrySizes=!0),null==t.strictFileNames&&(t.strictFileNames=!1),null==r&&(r=F),n.open(e,"r",(function(e,i){if(e)return r(e);c(i,t,(function(e,t){e&&n.close(i,F),r(e,t)}))}))},t.fromFd=c,t.fromBuffer=function(e,t,r){"function"==typeof t&&(r=t,t=null),null==t&&(t={}),t.autoClose=!1,null==t.lazyEntries&&(t.lazyEntries=!1),null==t.decodeStrings&&(t.decodeStrings=!0),null==t.validateEntrySizes&&(t.validateEntrySizes=!0),null==t.strictFileNames&&(t.strictFileNames=!1),p(o.createFromBuffer(e,{maxChunkSize:65536}),e.length,t,r)},t.fromRandomAccessReader=p,t.dosDateTimeToDate=g,t.validateFileName=w,t.ZipFile=h,t.Entry=E,t.RandomAccessReader=z,a.inherits(h,f),h.prototype.close=function(){this.isOpen&&(this.isOpen=!1,this.reader.unref())},h.prototype.readEntry=function(){if(!this.lazyEntries)throw new Error("readEntry() called without lazyEntries:true");this._readEntry()},h.prototype._readEntry=function(){var e=this;if(e.entryCount!==e.entriesRead){if(!e.emittedError){var t=b(46);v(e.reader,t,0,t.length,e.readEntryCursor,(function(r){if(r)return m(e,r);if(!e.emittedError){var n=new E,i=t.readUInt32LE(0);if(33639248!==i)return m(e,new Error("invalid central directory file header signature: 0x"+i.toString(16)));if(n.versionMadeBy=t.readUInt16LE(4),n.versionNeededToExtract=t.readUInt16LE(6),n.generalPurposeBitFlag=t.readUInt16LE(8),n.compressionMethod=t.readUInt16LE(10),n.lastModFileTime=t.readUInt16LE(12),n.lastModFileDate=t.readUInt16LE(14),n.crc32=t.readUInt32LE(16),n.compressedSize=t.readUInt32LE(20),n.uncompressedSize=t.readUInt32LE(24),n.fileNameLength=t.readUInt16LE(28),n.extraFieldLength=t.readUInt16LE(30),n.fileCommentLength=t.readUInt16LE(32),n.internalFileAttributes=t.readUInt16LE(36),n.externalFileAttributes=t.readUInt32LE(38),n.relativeOffsetOfLocalHeader=t.readUInt32LE(42),64&n.generalPurposeBitFlag)return m(e,new Error("strong encryption is not supported"));e.readEntryCursor+=46,t=b(n.fileNameLength+n.extraFieldLength+n.fileCommentLength),v(e.reader,t,0,t.length,e.readEntryCursor,(function(r){if(r)return m(e,r);if(!e.emittedError){var i=!!(2048&n.generalPurposeBitFlag);n.fileName=e.decodeStrings?C(t,0,n.fileNameLength,i):t.slice(0,n.fileNameLength);var o=n.fileNameLength+n.extraFieldLength,a=t.slice(n.fileNameLength,o);n.extraFields=[];for(var f=0;f<a.length-3;){var d=a.readUInt16LE(f+0),l=a.readUInt16LE(f+2),u=f+4,c=u+l;if(c>a.length)return m(e,new Error("extra field length exceeds extra field buffer size"));var p=b(l);a.copy(p,0,u,c),n.extraFields.push({id:d,data:p}),f=c}if(n.fileComment=e.decodeStrings?C(t,o,o+n.fileCommentLength,i):t.slice(o,o+n.fileCommentLength),n.comment=n.fileComment,e.readEntryCursor+=t.length,e.entriesRead+=1,4294967295===n.uncompressedSize||4294967295===n.compressedSize||4294967295===n.relativeOffsetOfLocalHeader){var h=null;for(f=0;f<n.extraFields.length;f++)if(1===(E=n.extraFields[f]).id){h=E.data;break}if(null==h)return m(e,new Error("expected zip64 extended information extra field"));var y=0;if(4294967295===n.uncompressedSize){if(y+8>h.length)return m(e,new Error("zip64 extended information extra field does not include uncompressed size"));n.uncompressedSize=I(h,y),y+=8}if(4294967295===n.compressedSize){if(y+8>h.length)return m(e,new Error("zip64 extended information extra field does not include compressed size"));n.compressedSize=I(h,y),y+=8}if(4294967295===n.relativeOffsetOfLocalHeader){if(y+8>h.length)return m(e,new Error("zip64 extended information extra field does not include relative header offset"));n.relativeOffsetOfLocalHeader=I(h,y),y+=8}}if(e.decodeStrings)for(f=0;f<n.extraFields.length;f++){var E;if(28789===(E=n.extraFields[f]).id){if(E.data.length<6)continue;if(1!==E.data.readUInt8(0))continue;var g=E.data.readUInt32LE(1);if(s.unsigned(t.slice(0,n.fileNameLength))!==g)continue;n.fileName=C(E.data,5,E.data.length,!0);break}}if(e.validateEntrySizes&&0===n.compressionMethod){var v=n.uncompressedSize;if(n.isEncrypted()&&(v+=12),n.compressedSize!==v){var x="compressed/uncompressed size mismatch for stored file: "+n.compressedSize+" != "+n.uncompressedSize;return m(e,new Error(x))}}if(e.decodeStrings){e.strictFileNames||(n.fileName=n.fileName.replace(/\\/g,"/"));var z=w(n.fileName,e.validateFileNameOptions);if(null!=z)return m(e,new Error(z))}e.emit("entry",n),e.lazyEntries||e._readEntry()}}))}}))}}else setImmediate((function(){e.autoClose&&e.close(),e.emittedError||e.emit("end")}))},h.prototype.openReadStream=function(e,t,r){var n=this,o=0,s=e.compressedSize;if(null==r)r=t,t={};else{if(null!=t.decrypt){if(!e.isEncrypted())throw new Error("options.decrypt can only be specified for encrypted entries");if(!1!==t.decrypt)throw new Error("invalid options.decrypt value: "+t.decrypt);if(e.isCompressed()&&!1!==t.decompress)throw new Error("entry is encrypted and compressed, and options.decompress !== false")}if(null!=t.decompress){if(!e.isCompressed())throw new Error("options.decompress can only be specified for compressed entries");if(!1!==t.decompress&&!0!==t.decompress)throw new Error("invalid options.decompress value: "+t.decompress)}if(null!=t.start||null!=t.end){if(e.isCompressed()&&!1!==t.decompress)throw new Error("start/end range not allowed for compressed entry without options.decompress === false");if(e.isEncrypted()&&!1!==t.decrypt)throw new Error("start/end range not allowed for encrypted entry without options.decrypt === false")}if(null!=t.start){if((o=t.start)<0)throw new Error("options.start < 0");if(o>e.compressedSize)throw new Error("options.start > entry.compressedSize")}if(null!=t.end){if((s=t.end)<0)throw new Error("options.end < 0");if(s>e.compressedSize)throw new Error("options.end > entry.compressedSize");if(s<o)throw new Error("options.end < options.start")}}if(!n.isOpen)return r(new Error("closed"));if(e.isEncrypted()&&!1!==t.decrypt)return r(new Error("entry is encrypted, and options.decrypt !== false"));n.reader.ref();var a=b(30);v(n.reader,a,0,a.length,e.relativeOffsetOfLocalHeader,(function(f){try{if(f)return r(f);var d=a.readUInt32LE(0);if(67324752!==d)return r(new Error("invalid local file header signature: 0x"+d.toString(16)));var l,u=a.readUInt16LE(26),c=a.readUInt16LE(28),p=e.relativeOffsetOfLocalHeader+a.length+u+c;if(0===e.compressionMethod)l=!1;else{if(8!==e.compressionMethod)return r(new Error("unsupported compression method: "+e.compressionMethod));l=null==t.decompress||t.decompress}var h=p,m=h+e.compressedSize;if(0!==e.compressedSize&&m>n.fileSize)return r(new Error("file data overflows file bounds: "+h+" + "+e.compressedSize+" > "+n.fileSize));var y=n.reader.createReadStream({start:h+o,end:h+s}),E=y;if(l){var g=!1,w=i.createInflateRaw();y.on("error",(function(e){setImmediate((function(){g||w.emit("error",e)}))})),y.pipe(w),n.validateEntrySizes?(E=new x(e.uncompressedSize),w.on("error",(function(e){setImmediate((function(){g||E.emit("error",e)}))})),w.pipe(E)):E=w,E.destroy=function(){g=!0,w!==E&&w.unpipe(E),y.unpipe(w),y.destroy()}}r(null,E)}finally{n.reader.unref()}}))},E.prototype.getLastModDate=function(){return g(this.lastModFileDate,this.lastModFileTime)},E.prototype.isEncrypted=function(){return!!(1&this.generalPurposeBitFlag)},E.prototype.isCompressed=function(){return 8===this.compressionMethod},a.inherits(x,d),x.prototype._transform=function(e,t,r){if(this.actualByteCount+=e.length,this.actualByteCount>this.expectedByteCount){var n="too many bytes in the stream. expected "+this.expectedByteCount+". got at least "+this.actualByteCount;return r(new Error(n))}r(null,e)},x.prototype._flush=function(e){if(this.actualByteCount<this.expectedByteCount){var t="not enough bytes in the stream. expected "+this.expectedByteCount+". got only "+this.actualByteCount;return e(new Error(t))}e()},a.inherits(z,f),z.prototype.ref=function(){this.refCount+=1},z.prototype.unref=function(){var e=this;if(e.refCount-=1,!(e.refCount>0)){if(e.refCount<0)throw new Error("invalid unref");e.close((function(t){if(t)return e.emit("error",t);e.emit("close")}))}},z.prototype.createReadStream=function(e){var t=e.start,r=e.end;if(t===r){var n=new l;return setImmediate((function(){n.end()})),n}var i=this._readStreamForRange(t,r),o=!1,s=new S(this);i.on("error",(function(e){setImmediate((function(){o||s.emit("error",e)}))})),s.destroy=function(){i.unpipe(s),s.unref(),i.destroy()};var a=new x(r-t);return s.on("error",(function(e){setImmediate((function(){o||a.emit("error",e)}))})),a.destroy=function(){o=!0,s.unpipe(a),s.destroy()},i.pipe(s).pipe(a)},z.prototype._readStreamForRange=function(e,t){throw new Error("not implemented")},z.prototype.read=function(e,t,r,n,i){var o=this.createReadStream({start:n,end:n+r}),s=new u,a=0;s._write=function(r,n,i){r.copy(e,t+a,0,r.length),a+=r.length,i()},s.on("finish",i),o.on("error",(function(e){i(e)})),o.pipe(s)},z.prototype.close=function(e){setImmediate(e)},a.inherits(S,l),S.prototype._flush=function(e){this.unref(),e()},S.prototype.unref=function(e){this.unreffedYet||(this.unreffedYet=!0,this.context.unref())};var b,L="\0☺☻♥♦♣♠•◘○◙♂♀♪♫☼►◄↕‼¶§▬↨↑↓→←∟↔▲▼ !\"#$%&'()*+,-./**********:;<=>?@ABCDEFGHIJKLMNOPQRSTUVWXYZ[\\]^_`abcdefghijklmnopqrstuvwxyz{|}~⌂ÇüéâäàåçêëèïîìÄÅÉæÆôöòûùÿÖÜ¢£¥₧ƒáíóúñÑªº¿⌐¬½¼¡«»░▒▓│┤╡╢╖╕╣║╗╝╜╛┐└┴┬├─┼╞╟╚╔╩╦╠═╬╧╨╤╥╙╘╒╓╫╪┘┌█▄▌▐▀αßΓπΣσµτΦΘΩδ∞φε∩≡±≥≤⌠⌡÷≈°∙·√ⁿ²■ ";function C(e,t,r,n){if(n)return e.toString("utf8",t,r);for(var i="",o=t;o<r;o++)i+=L[e[o]];return i}function I(e,t){var r=e.readUInt32LE(t);return 4294967296*e.readUInt32LE(t+4)+r}function F(e){if(e)throw e}b="function"==typeof Buffer.allocUnsafe?function(e){return Buffer.allocUnsafe(e)}:function(e){return new Buffer(e)}}};
//# sourceMappingURL=https://cursor-sourcemaps.s3.amazonaws.com/sourcemaps/b6fb41b5f36bda05cab7109606e7404a65d1ff30/extensions/cursor-retrieval/dist/832.main.js.map
//# debugId=3665b3c4-88ef-5b36-af86-68271592678e
