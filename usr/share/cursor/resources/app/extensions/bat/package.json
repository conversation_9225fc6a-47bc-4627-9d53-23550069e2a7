{"name": "bat", "displayName": "%displayName%", "description": "%description%", "version": "1.0.0", "publisher": "vscode", "license": "MIT", "engines": {"vscode": "^1.52.0"}, "scripts": {"update-grammar": "node ../node_modules/vscode-grammar-updater/bin mmims/language-batchfile grammars/batchfile.cson ./syntaxes/batchfile.tmLanguage.json"}, "categories": ["Programming Languages"], "contributes": {"languages": [{"id": "bat", "extensions": [".bat", ".cmd"], "aliases": ["<PERSON><PERSON>", "bat"], "configuration": "./language-configuration.json"}], "grammars": [{"language": "bat", "scopeName": "source.batchfile", "path": "./syntaxes/batchfile.tmLanguage.json"}], "snippets": [{"language": "bat", "path": "./snippets/batchfile.code-snippets"}]}, "repository": {"type": "git", "url": "https://github.com/microsoft/vscode.git"}}