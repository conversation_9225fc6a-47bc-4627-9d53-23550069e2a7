/*! For license information please see configurationEditingMain.js.LICENSE.txt */

!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},n=(new e.Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="18fcbbf9-feff-5ce8-a8c0-ebf8f36906f2")}catch(e){}}();
(()=>{var e={125:(e,p,a)=>{"use strict";function d(){return"object"==typeof navigator&&"userAgent"in navigator?navigator.userAgent:"object"==typeof process&&"version"in process?`Node.js/${process.version.substr(1)} (${process.platform}; ${process.arch})`:"<environment undetectable>"}a.r(p),a.d(p,{Octokit:()=>Ke});var t=a(811);function r(e){return"[object Object]"===Object.prototype.toString.call(e)}function i(e){var p,a;return!1!==r(e)&&(void 0===(p=e.constructor)||!1!==r(a=p.prototype)&&!1!==a.hasOwnProperty("isPrototypeOf"))}function s(e,p){const a=Object.assign({},e);return Object.keys(p).forEach((d=>{i(p[d])?d in e?a[d]=s(e[d],p[d]):Object.assign(a,{[d]:p[d]}):Object.assign(a,{[d]:p[d]})})),a}function o(e){for(const p in e)void 0===e[p]&&delete e[p];return e}function m(e,p,a){if("string"==typeof p){let[e,d]=p.split(" ");a=Object.assign(d?{method:e,url:d}:{url:e},a)}else a=Object.assign({},p);var d;a.headers=(d=a.headers)?Object.keys(d).reduce(((e,p)=>(e[p.toLowerCase()]=d[p],e)),{}):{},o(a),o(a.headers);const t=s(e||{},a);return e&&e.mediaType.previews.length&&(t.mediaType.previews=e.mediaType.previews.filter((e=>!t.mediaType.previews.includes(e))).concat(t.mediaType.previews)),t.mediaType.previews=t.mediaType.previews.map((e=>e.replace(/-preview/,""))),t}const l=/\{[^}]+\}/g;function n(e){return e.replace(/^\W+|\W+$/g,"").split(/,/)}function u(e,p){return Object.keys(e).filter((e=>!p.includes(e))).reduce(((p,a)=>(p[a]=e[a],p)),{})}function c(e){return e.split(/(%[0-9A-Fa-f]{2})/g).map((function(e){return/%[0-9A-Fa-f]/.test(e)||(e=encodeURI(e).replace(/%5B/g,"[").replace(/%5D/g,"]")),e})).join("")}function v(e){return encodeURIComponent(e).replace(/[!'()*]/g,(function(e){return"%"+e.charCodeAt(0).toString(16).toUpperCase()}))}function h(e,p,a){return p="+"===e||"#"===e?c(p):v(p),a?v(a)+"="+p:p}function g(e){return null!=e}function w(e){return";"===e||"&"===e||"?"===e}function f(e,p){var a=["+","#",".","/",";","?","&"];return e.replace(/\{([^\{\}]+)\}|([^\{\}]+)/g,(function(e,d,t){if(d){let e="";const t=[];if(-1!==a.indexOf(d.charAt(0))&&(e=d.charAt(0),d=d.substr(1)),d.split(/,/g).forEach((function(a){var d=/([^:\*]*)(?::(\d+)|(\*))?/.exec(a);t.push(function(e,p,a,d){var t=e[a],r=[];if(g(t)&&""!==t)if("string"==typeof t||"number"==typeof t||"boolean"==typeof t)t=t.toString(),d&&"*"!==d&&(t=t.substring(0,parseInt(d,10))),r.push(h(p,t,w(p)?a:""));else if("*"===d)Array.isArray(t)?t.filter(g).forEach((function(e){r.push(h(p,e,w(p)?a:""))})):Object.keys(t).forEach((function(e){g(t[e])&&r.push(h(p,t[e],e))}));else{const e=[];Array.isArray(t)?t.filter(g).forEach((function(a){e.push(h(p,a))})):Object.keys(t).forEach((function(a){g(t[a])&&(e.push(v(a)),e.push(h(p,t[a].toString())))})),w(p)?r.push(v(a)+"="+e.join(",")):0!==e.length&&r.push(e.join(","))}else";"===p?g(t)&&r.push(v(a)):""!==t||"&"!==p&&"?"!==p?""===t&&r.push(""):r.push(v(a)+"=");return r}(p,e,d[1],d[2]||d[3]))})),e&&"+"!==e){var r=",";return"?"===e?r="&":"#"!==e&&(r=e),(0!==t.length?e:"")+t.join(r)}return t.join(",")}return c(t)}))}function T(e){let p,a=e.method.toUpperCase(),d=(e.url||"/").replace(/:([a-z]\w+)/g,"{$1}"),t=Object.assign({},e.headers),r=u(e,["method","baseUrl","url","headers","request","mediaType"]);const i=function(e){const p=e.match(l);return p?p.map(n).reduce(((e,p)=>e.concat(p)),[]):[]}(d);var s;d=(s=d,{expand:f.bind(null,s)}).expand(r),/^http/.test(d)||(d=e.baseUrl+d);const o=u(r,Object.keys(e).filter((e=>i.includes(e))).concat("baseUrl"));if(!/application\/octet-stream/i.test(t.accept)&&(e.mediaType.format&&(t.accept=t.accept.split(/,/).map((p=>p.replace(/application\/vnd(\.\w+)(\.v3)?(\.\w+)?(\+json)?$/,`application/vnd$1$2.${e.mediaType.format}`))).join(",")),e.mediaType.previews.length)){const p=t.accept.match(/[\w-]+(?=-preview)/g)||[];t.accept=p.concat(e.mediaType.previews).map((p=>`application/vnd.github.${p}-preview${e.mediaType.format?`.${e.mediaType.format}`:"+json"}`)).join(",")}return["GET","HEAD"].includes(a)?d=function(e,p){const a=/\?/.test(e)?"&":"?",d=Object.keys(p);return 0===d.length?e:e+a+d.map((e=>"q"===e?"q="+p.q.split("+").map(encodeURIComponent).join("+"):`${e}=${encodeURIComponent(p[e])}`)).join("&")}(d,o):"data"in o?p=o.data:Object.keys(o).length&&(p=o),t["content-type"]||void 0===p||(t["content-type"]="application/json; charset=utf-8"),["PATCH","PUT"].includes(a)&&void 0===p&&(p=""),Object.assign({method:a,url:d,headers:t},void 0!==p?{body:p}:null,e.request?{request:e.request}:null)}function b(e,p,a){return T(m(e,p,a))}const _=function e(p,a){const d=m(p,a),t=b.bind(null,d);return Object.assign(t,{DEFAULTS:d,defaults:e.bind(null,d),merge:m.bind(null,d),parse:T})}(null,{method:"GET",baseUrl:"https://api.github.com",headers:{accept:"application/vnd.github.v3+json","user-agent":`octokit-endpoint.js/7.0.6 ${d()}`},mediaType:{format:"",previews:[]}}),E=require("stream");var y=a(611),S=a(16),k=a(417),D=a(692);const A=require("zlib"),N=E.Readable,P=Symbol("buffer"),O=Symbol("type");class V{constructor(){this[O]="";const e=arguments[0],p=arguments[1],a=[];let d=0;if(e){const p=e,t=Number(p.length);for(let e=0;e<t;e++){const t=p[e];let r;r=t instanceof Buffer?t:ArrayBuffer.isView(t)?Buffer.from(t.buffer,t.byteOffset,t.byteLength):t instanceof ArrayBuffer?Buffer.from(t):t instanceof V?t[P]:Buffer.from("string"==typeof t?t:String(t)),d+=r.length,a.push(r)}}this[P]=Buffer.concat(a);let t=p&&void 0!==p.type&&String(p.type).toLowerCase();t&&!/[^\u0020-\u007E]/.test(t)&&(this[O]=t)}get size(){return this[P].length}get type(){return this[O]}text(){return Promise.resolve(this[P].toString())}arrayBuffer(){const e=this[P],p=e.buffer.slice(e.byteOffset,e.byteOffset+e.byteLength);return Promise.resolve(p)}stream(){const e=new N;return e._read=function(){},e.push(this[P]),e.push(null),e}toString(){return"[object Blob]"}slice(){const e=this.size,p=arguments[0],a=arguments[1];let d,t;d=void 0===p?0:p<0?Math.max(e+p,0):Math.min(p,e),t=void 0===a?e:a<0?Math.max(e+a,0):Math.min(a,e);const r=Math.max(t-d,0),i=this[P].slice(d,d+r),s=new V([],{type:arguments[2]});return s[P]=i,s}}function C(e,p,a){Error.call(this,e),this.message=e,this.type=p,a&&(this.code=this.errno=a.code),Error.captureStackTrace(this,this.constructor)}let F;Object.defineProperties(V.prototype,{size:{enumerable:!0},type:{enumerable:!0},slice:{enumerable:!0}}),Object.defineProperty(V.prototype,Symbol.toStringTag,{value:"Blob",writable:!1,enumerable:!1,configurable:!0}),C.prototype=Object.create(Error.prototype),C.prototype.constructor=C,C.prototype.name="FetchError";try{F=require("encoding").convert}catch(e){}const G=Symbol("Body internals"),R=E.PassThrough;function U(e){var p=this,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},d=a.size;let t=void 0===d?0:d;var r=a.timeout;let i=void 0===r?0:r;null==e?e=null:x(e)?e=Buffer.from(e.toString()):B(e)||Buffer.isBuffer(e)||("[object ArrayBuffer]"===Object.prototype.toString.call(e)?e=Buffer.from(e):ArrayBuffer.isView(e)?e=Buffer.from(e.buffer,e.byteOffset,e.byteLength):e instanceof E||(e=Buffer.from(String(e)))),this[G]={body:e,disturbed:!1,error:null},this.size=t,this.timeout=i,e instanceof E&&e.on("error",(function(e){const a="AbortError"===e.name?e:new C(`Invalid response body while trying to fetch ${p.url}: ${e.message}`,"system",e);p[G].error=a}))}function L(){var e=this;if(this[G].disturbed)return U.Promise.reject(new TypeError(`body used already for: ${this.url}`));if(this[G].disturbed=!0,this[G].error)return U.Promise.reject(this[G].error);let p=this.body;if(null===p)return U.Promise.resolve(Buffer.alloc(0));if(B(p)&&(p=p.stream()),Buffer.isBuffer(p))return U.Promise.resolve(p);if(!(p instanceof E))return U.Promise.resolve(Buffer.alloc(0));let a=[],d=0,t=!1;return new U.Promise((function(r,i){let s;e.timeout&&(s=setTimeout((function(){t=!0,i(new C(`Response timeout while trying to fetch ${e.url} (over ${e.timeout}ms)`,"body-timeout"))}),e.timeout)),p.on("error",(function(p){"AbortError"===p.name?(t=!0,i(p)):i(new C(`Invalid response body while trying to fetch ${e.url}: ${p.message}`,"system",p))})),p.on("data",(function(p){if(!t&&null!==p){if(e.size&&d+p.length>e.size)return t=!0,void i(new C(`content size at ${e.url} over limit: ${e.size}`,"max-size"));d+=p.length,a.push(p)}})),p.on("end",(function(){if(!t){clearTimeout(s);try{r(Buffer.concat(a,d))}catch(p){i(new C(`Could not create Buffer from response body for ${e.url}: ${p.message}`,"system",p))}}}))}))}function x(e){return"object"==typeof e&&"function"==typeof e.append&&"function"==typeof e.delete&&"function"==typeof e.get&&"function"==typeof e.getAll&&"function"==typeof e.has&&"function"==typeof e.set&&("URLSearchParams"===e.constructor.name||"[object URLSearchParams]"===Object.prototype.toString.call(e)||"function"==typeof e.sort)}function B(e){return"object"==typeof e&&"function"==typeof e.arrayBuffer&&"string"==typeof e.type&&"function"==typeof e.stream&&"function"==typeof e.constructor&&"string"==typeof e.constructor.name&&/^(Blob|File)$/.test(e.constructor.name)&&/^(Blob|File)$/.test(e[Symbol.toStringTag])}function j(e){let p,a,d=e.body;if(e.bodyUsed)throw new Error("cannot clone body after it is used");return d instanceof E&&"function"!=typeof d.getBoundary&&(p=new R,a=new R,d.pipe(p),d.pipe(a),e[G].body=p,d=a),d}function I(e){return null===e?null:"string"==typeof e?"text/plain;charset=UTF-8":x(e)?"application/x-www-form-urlencoded;charset=UTF-8":B(e)?e.type||null:Buffer.isBuffer(e)||"[object ArrayBuffer]"===Object.prototype.toString.call(e)||ArrayBuffer.isView(e)?null:"function"==typeof e.getBoundary?`multipart/form-data;boundary=${e.getBoundary()}`:e instanceof E?null:"text/plain;charset=UTF-8"}function q(e){const p=e.body;return null===p?0:B(p)?p.size:Buffer.isBuffer(p)?p.length:p&&"function"==typeof p.getLengthSync&&(p._lengthRetrievers&&0==p._lengthRetrievers.length||p.hasKnownLength&&p.hasKnownLength())?p.getLengthSync():null}U.prototype={get body(){return this[G].body},get bodyUsed(){return this[G].disturbed},arrayBuffer(){return L.call(this).then((function(e){return e.buffer.slice(e.byteOffset,e.byteOffset+e.byteLength)}))},blob(){let e=this.headers&&this.headers.get("content-type")||"";return L.call(this).then((function(p){return Object.assign(new V([],{type:e.toLowerCase()}),{[P]:p})}))},json(){var e=this;return L.call(this).then((function(p){try{return JSON.parse(p.toString())}catch(p){return U.Promise.reject(new C(`invalid json response body at ${e.url} reason: ${p.message}`,"invalid-json"))}}))},text(){return L.call(this).then((function(e){return e.toString()}))},buffer(){return L.call(this)},textConverted(){var e=this;return L.call(this).then((function(p){return function(e,p){if("function"!=typeof F)throw new Error("The package `encoding` must be installed to use the textConverted() function");const a=p.get("content-type");let d,t,r="utf-8";return a&&(d=/charset=([^;]*)/i.exec(a)),t=e.slice(0,1024).toString(),!d&&t&&(d=/<meta.+?charset=(['"])(.+?)\1/i.exec(t)),!d&&t&&(d=/<meta[\s]+?http-equiv=(['"])content-type\1[\s]+?content=(['"])(.+?)\2/i.exec(t),d||(d=/<meta[\s]+?content=(['"])(.+?)\1[\s]+?http-equiv=(['"])content-type\3/i.exec(t),d&&d.pop()),d&&(d=/charset=(.*)/i.exec(d.pop()))),!d&&t&&(d=/<\?xml.+?encoding=(['"])(.+?)\1/i.exec(t)),d&&(r=d.pop(),"gb2312"!==r&&"gbk"!==r||(r="gb18030")),F(e,"UTF-8",r).toString()}(p,e.headers)}))}},Object.defineProperties(U.prototype,{body:{enumerable:!0},bodyUsed:{enumerable:!0},arrayBuffer:{enumerable:!0},blob:{enumerable:!0},json:{enumerable:!0},text:{enumerable:!0}}),U.mixIn=function(e){for(const p of Object.getOwnPropertyNames(U.prototype))if(!(p in e)){const a=Object.getOwnPropertyDescriptor(U.prototype,p);Object.defineProperty(e,p,a)}},U.Promise=global.Promise;const $=/[^\^_`a-zA-Z\-0-9!#$%&'*+.|~]/,H=/[^\t\x20-\x7e\x80-\xff]/;function M(e){if(e=`${e}`,$.test(e)||""===e)throw new TypeError(`${e} is not a legal HTTP header name`)}function z(e){if(e=`${e}`,H.test(e))throw new TypeError(`${e} is not a legal HTTP header value`)}function W(e,p){p=p.toLowerCase();for(const a in e)if(a.toLowerCase()===p)return a}const K=Symbol("map");class J{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:void 0;if(this[K]=Object.create(null),e instanceof J){const p=e.raw(),a=Object.keys(p);for(const e of a)for(const a of p[e])this.append(e,a)}else if(null==e);else{if("object"!=typeof e)throw new TypeError("Provided initializer must be an object");{const p=e[Symbol.iterator];if(null!=p){if("function"!=typeof p)throw new TypeError("Header pairs must be iterable");const a=[];for(const p of e){if("object"!=typeof p||"function"!=typeof p[Symbol.iterator])throw new TypeError("Each header pair must be iterable");a.push(Array.from(p))}for(const e of a){if(2!==e.length)throw new TypeError("Each header pair must be a name/value tuple");this.append(e[0],e[1])}}else for(const p of Object.keys(e)){const a=e[p];this.append(p,a)}}}}get(e){M(e=`${e}`);const p=W(this[K],e);return void 0===p?null:this[K][p].join(", ")}forEach(e){let p=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,a=Z(this),d=0;for(;d<a.length;){var t=a[d];const r=t[0],i=t[1];e.call(p,i,r,this),a=Z(this),d++}}set(e,p){p=`${p}`,M(e=`${e}`),z(p);const a=W(this[K],e);this[K][void 0!==a?a:e]=[p]}append(e,p){p=`${p}`,M(e=`${e}`),z(p);const a=W(this[K],e);void 0!==a?this[K][a].push(p):this[K][e]=[p]}has(e){return M(e=`${e}`),void 0!==W(this[K],e)}delete(e){M(e=`${e}`);const p=W(this[K],e);void 0!==p&&delete this[K][p]}raw(){return this[K]}keys(){return X(this,"key")}values(){return X(this,"value")}[Symbol.iterator](){return X(this,"key+value")}}function Z(e){let p=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"key+value";return Object.keys(e[K]).sort().map("key"===p?function(e){return e.toLowerCase()}:"value"===p?function(p){return e[K][p].join(", ")}:function(p){return[p.toLowerCase(),e[K][p].join(", ")]})}J.prototype.entries=J.prototype[Symbol.iterator],Object.defineProperty(J.prototype,Symbol.toStringTag,{value:"Headers",writable:!1,enumerable:!1,configurable:!0}),Object.defineProperties(J.prototype,{get:{enumerable:!0},forEach:{enumerable:!0},set:{enumerable:!0},append:{enumerable:!0},has:{enumerable:!0},delete:{enumerable:!0},keys:{enumerable:!0},values:{enumerable:!0},entries:{enumerable:!0}});const Y=Symbol("internal");function X(e,p){const a=Object.create(Q);return a[Y]={target:e,kind:p,index:0},a}const Q=Object.setPrototypeOf({next(){if(!this||Object.getPrototypeOf(this)!==Q)throw new TypeError("Value of `this` is not a HeadersIterator");var e=this[Y];const p=e.target,a=e.kind,d=e.index,t=Z(p,a);return d>=t.length?{value:void 0,done:!0}:(this[Y].index=d+1,{value:t[d],done:!1})}},Object.getPrototypeOf(Object.getPrototypeOf([][Symbol.iterator]())));function ee(e){const p=Object.assign({__proto__:null},e[K]),a=W(e[K],"Host");return void 0!==a&&(p[a]=p[a][0]),p}Object.defineProperty(Q,Symbol.toStringTag,{value:"HeadersIterator",writable:!1,enumerable:!1,configurable:!0});const pe=Symbol("Response internals"),ae=y.STATUS_CODES;class de{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,p=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};U.call(this,e,p);const a=p.status||200,d=new J(p.headers);if(null!=e&&!d.has("Content-Type")){const p=I(e);p&&d.append("Content-Type",p)}this[pe]={url:p.url,status:a,statusText:p.statusText||ae[a],headers:d,counter:p.counter}}get url(){return this[pe].url||""}get status(){return this[pe].status}get ok(){return this[pe].status>=200&&this[pe].status<300}get redirected(){return this[pe].counter>0}get statusText(){return this[pe].statusText}get headers(){return this[pe].headers}clone(){return new de(j(this),{url:this.url,status:this.status,statusText:this.statusText,headers:this.headers,ok:this.ok,redirected:this.redirected})}}U.mixIn(de.prototype),Object.defineProperties(de.prototype,{url:{enumerable:!0},status:{enumerable:!0},ok:{enumerable:!0},redirected:{enumerable:!0},statusText:{enumerable:!0},headers:{enumerable:!0},clone:{enumerable:!0}}),Object.defineProperty(de.prototype,Symbol.toStringTag,{value:"Response",writable:!1,enumerable:!1,configurable:!0});const te=Symbol("Request internals"),re=S.URL||k.URL,ie=S.parse,se=S.format;function oe(e){return/^[a-zA-Z][a-zA-Z\d+\-.]*:/.exec(e)&&(e=new re(e).toString()),ie(e)}const me="destroy"in E.Readable.prototype;function le(e){return"object"==typeof e&&"object"==typeof e[te]}class ne{constructor(e){let p,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};le(e)?p=oe(e.url):(p=e&&e.href?oe(e.href):oe(`${e}`),e={});let d=a.method||e.method||"GET";if(d=d.toUpperCase(),(null!=a.body||le(e)&&null!==e.body)&&("GET"===d||"HEAD"===d))throw new TypeError("Request with GET/HEAD method cannot have body");let t=null!=a.body?a.body:le(e)&&null!==e.body?j(e):null;U.call(this,t,{timeout:a.timeout||e.timeout||0,size:a.size||e.size||0});const r=new J(a.headers||e.headers||{});if(null!=t&&!r.has("Content-Type")){const e=I(t);e&&r.append("Content-Type",e)}let i=le(e)?e.signal:null;if("signal"in a&&(i=a.signal),null!=i&&!function(e){const p=e&&"object"==typeof e&&Object.getPrototypeOf(e);return!(!p||"AbortSignal"!==p.constructor.name)}(i))throw new TypeError("Expected signal to be an instanceof AbortSignal");this[te]={method:d,redirect:a.redirect||e.redirect||"follow",headers:r,parsedURL:p,signal:i},this.follow=void 0!==a.follow?a.follow:void 0!==e.follow?e.follow:20,this.compress=void 0!==a.compress?a.compress:void 0===e.compress||e.compress,this.counter=a.counter||e.counter||0,this.agent=a.agent||e.agent}get method(){return this[te].method}get url(){return se(this[te].parsedURL)}get headers(){return this[te].headers}get redirect(){return this[te].redirect}get signal(){return this[te].signal}clone(){return new ne(this)}}function ue(e){Error.call(this,e),this.type="aborted",this.message=e,Error.captureStackTrace(this,this.constructor)}U.mixIn(ne.prototype),Object.defineProperty(ne.prototype,Symbol.toStringTag,{value:"Request",writable:!1,enumerable:!1,configurable:!0}),Object.defineProperties(ne.prototype,{method:{enumerable:!0},url:{enumerable:!0},headers:{enumerable:!0},redirect:{enumerable:!0},clone:{enumerable:!0},signal:{enumerable:!0}}),ue.prototype=Object.create(Error.prototype),ue.prototype.constructor=ue,ue.prototype.name="AbortError";const ce=S.URL||k.URL,ve=E.PassThrough;function he(e,p){if(!he.Promise)throw new Error("native promise missing, set fetch.Promise to your favorite alternative");return U.Promise=he.Promise,new he.Promise((function(a,d){const t=new ne(e,p),r=function(e){const p=e[te].parsedURL,a=new J(e[te].headers);if(a.has("Accept")||a.set("Accept","*/*"),!p.protocol||!p.hostname)throw new TypeError("Only absolute URLs are supported");if(!/^https?:$/.test(p.protocol))throw new TypeError("Only HTTP(S) protocols are supported");if(e.signal&&e.body instanceof E.Readable&&!me)throw new Error("Cancellation of streamed requests with AbortSignal is not supported in node < 8");let d=null;if(null==e.body&&/^(POST|PUT)$/i.test(e.method)&&(d="0"),null!=e.body){const p=q(e);"number"==typeof p&&(d=String(p))}d&&a.set("Content-Length",d),a.has("User-Agent")||a.set("User-Agent","node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"),e.compress&&!a.has("Accept-Encoding")&&a.set("Accept-Encoding","gzip,deflate");let t=e.agent;return"function"==typeof t&&(t=t(p)),Object.assign({},p,{method:e.method,headers:ee(a),agent:t})}(t),i=("https:"===r.protocol?D:y).request,s=t.signal;let o=null;const m=function(){let e=new ue("The user aborted a request.");d(e),t.body&&t.body instanceof E.Readable&&ge(t.body,e),o&&o.body&&o.body.emit("error",e)};if(s&&s.aborted)return void m();const l=function(){m(),c()},n=i(r);let u;function c(){n.abort(),s&&s.removeEventListener("abort",l),clearTimeout(u)}s&&s.addEventListener("abort",l),t.timeout&&n.once("socket",(function(e){u=setTimeout((function(){d(new C(`network timeout at: ${t.url}`,"request-timeout")),c()}),t.timeout)})),n.on("error",(function(e){d(new C(`request to ${t.url} failed, reason: ${e.message}`,"system",e)),o&&o.body&&ge(o.body,e),c()})),function(e,p){let a;e.on("socket",(function(e){a=e})),e.on("response",(function(e){const d=e.headers;"chunked"!==d["transfer-encoding"]||d["content-length"]||e.once("close",(function(e){if(a&&a.listenerCount("data")>0&&!e){const e=new Error("Premature close");e.code="ERR_STREAM_PREMATURE_CLOSE",p(e)}}))}))}(n,(function(e){s&&s.aborted||o&&o.body&&ge(o.body,e)})),parseInt(process.version.substring(1))<14&&n.on("socket",(function(e){e.addListener("close",(function(p){const a=e.listenerCount("data")>0;if(o&&a&&!p&&(!s||!s.aborted)){const e=new Error("Premature close");e.code="ERR_STREAM_PREMATURE_CLOSE",o.body.emit("error",e)}}))})),n.on("response",(function(e){clearTimeout(u);const p=function(e){const p=new J;for(const a of Object.keys(e))if(!$.test(a))if(Array.isArray(e[a]))for(const d of e[a])H.test(d)||(void 0===p[K][a]?p[K][a]=[d]:p[K][a].push(d));else H.test(e[a])||(p[K][a]=[e[a]]);return p}(e.headers);if(he.isRedirect(e.statusCode)){const i=p.get("Location");let s=null;try{s=null===i?null:new ce(i,t.url).toString()}catch(e){if("manual"!==t.redirect)return d(new C(`uri requested responds with an invalid redirect URL: ${i}`,"invalid-redirect")),void c()}switch(t.redirect){case"error":return d(new C(`uri requested responds with a redirect, redirect mode is set to error: ${t.url}`,"no-redirect")),void c();case"manual":if(null!==s)try{p.set("Location",s)}catch(e){d(e)}break;case"follow":if(null===s)break;if(t.counter>=t.follow)return d(new C(`maximum redirect reached at: ${t.url}`,"max-redirect")),void c();const i={headers:new J(t.headers),follow:t.follow,counter:t.counter+1,agent:t.agent,compress:t.compress,method:t.method,body:t.body,signal:t.signal,timeout:t.timeout,size:t.size};if(!function(e,p){const a=new ce(p).hostname,d=new ce(e).hostname;return a===d||"."===a[a.length-d.length-1]&&a.endsWith(d)}(t.url,s)||(r=t.url,new ce(s).protocol!==new ce(r).protocol))for(const e of["authorization","www-authenticate","cookie","cookie2"])i.headers.delete(e);return 303!==e.statusCode&&t.body&&null===q(t)?(d(new C("Cannot follow redirect with body being a readable stream","unsupported-redirect")),void c()):(303!==e.statusCode&&(301!==e.statusCode&&302!==e.statusCode||"POST"!==t.method)||(i.method="GET",i.body=void 0,i.headers.delete("content-length")),a(he(new ne(s,i))),void c())}}var r;e.once("end",(function(){s&&s.removeEventListener("abort",l)}));let i=e.pipe(new ve);const m={url:t.url,status:e.statusCode,statusText:e.statusMessage,headers:p,size:t.size,timeout:t.timeout,counter:t.counter},n=p.get("Content-Encoding");if(!t.compress||"HEAD"===t.method||null===n||204===e.statusCode||304===e.statusCode)return o=new de(i,m),void a(o);const v={flush:A.Z_SYNC_FLUSH,finishFlush:A.Z_SYNC_FLUSH};if("gzip"==n||"x-gzip"==n)return i=i.pipe(A.createGunzip(v)),o=new de(i,m),void a(o);if("deflate"==n||"x-deflate"==n){const p=e.pipe(new ve);return p.once("data",(function(e){i=8==(15&e[0])?i.pipe(A.createInflate()):i.pipe(A.createInflateRaw()),o=new de(i,m),a(o)})),void p.on("end",(function(){o||(o=new de(i,m),a(o))}))}if("br"==n&&"function"==typeof A.createBrotliDecompress)return i=i.pipe(A.createBrotliDecompress()),o=new de(i,m),void a(o);o=new de(i,m),a(o)})),function(e,p){const a=p.body;null===a?e.end():B(a)?a.stream().pipe(e):Buffer.isBuffer(a)?(e.write(a),e.end()):a.pipe(e)}(n,t)}))}function ge(e,p){e.destroy?e.destroy(p):(e.emit("error",p),e.end())}he.isRedirect=function(e){return 301===e||302===e||303===e||307===e||308===e},he.Promise=global.Promise;const we=he;class fe extends Error{constructor(e){super(e),Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor),this.name="Deprecation"}}var Te=a(519),be=a.n(Te);const _e=be()((e=>console.warn(e))),Ee=be()((e=>console.warn(e)));class ye extends Error{constructor(e,p,a){let d;super(e),Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor),this.name="HttpError",this.status=p,"headers"in a&&void 0!==a.headers&&(d=a.headers),"response"in a&&(this.response=a.response,d=a.response.headers);const t=Object.assign({},a.request);a.request.headers.authorization&&(t.headers=Object.assign({},a.request.headers,{authorization:a.request.headers.authorization.replace(/ .*$/," [REDACTED]")})),t.url=t.url.replace(/\bclient_secret=\w+/g,"client_secret=[REDACTED]").replace(/\baccess_token=\w+/g,"access_token=[REDACTED]"),this.request=t,Object.defineProperty(this,"code",{get:()=>(_e(new fe("[@octokit/request-error] `error.code` is deprecated, use `error.status`.")),p)}),Object.defineProperty(this,"headers",{get:()=>(Ee(new fe("[@octokit/request-error] `error.headers` is deprecated, use `error.response.headers`.")),d||{})})}}function Se(e){const p=e.request&&e.request.log?e.request.log:console;(i(e.body)||Array.isArray(e.body))&&(e.body=JSON.stringify(e.body));let a,d,t={};return(e.request&&e.request.fetch||globalThis.fetch||we)(e.url,Object.assign({method:e.method,body:e.body,headers:e.headers,redirect:e.redirect,...e.body&&{duplex:"half"}},e.request)).then((async r=>{d=r.url,a=r.status;for(const e of r.headers)t[e[0]]=e[1];if("deprecation"in t){const a=t.link&&t.link.match(/<([^>]+)>; rel="deprecation"/),d=a&&a.pop();p.warn(`[@octokit/request] "${e.method} ${e.url}" is deprecated. It is scheduled to be removed on ${t.sunset}${d?`. See ${d}`:""}`)}if(204!==a&&205!==a){if("HEAD"===e.method){if(a<400)return;throw new ye(r.statusText,a,{response:{url:d,status:a,headers:t,data:void 0},request:e})}if(304===a)throw new ye("Not modified",a,{response:{url:d,status:a,headers:t,data:await ke(r)},request:e});if(a>=400){const p=await ke(r),i=new ye(function(e){return"string"==typeof e?e:"message"in e?Array.isArray(e.errors)?`${e.message}: ${e.errors.map(JSON.stringify).join(", ")}`:e.message:`Unknown error: ${JSON.stringify(e)}`}(p),a,{response:{url:d,status:a,headers:t,data:p},request:e});throw i}return ke(r)}})).then((e=>({status:a,url:d,headers:t,data:e}))).catch((p=>{if(p instanceof ye)throw p;if("AbortError"===p.name)throw p;throw new ye(p.message,500,{request:e})}))}async function ke(e){const p=e.headers.get("content-type");return/application\/json/.test(p)?e.json():!p||/^text\/|charset=utf-8$/.test(p)?e.text():function(e){return e.arrayBuffer()}(e)}const De=function e(p,a){const d=p.defaults(a);return Object.assign((function(p,a){const t=d.merge(p,a);if(!t.request||!t.request.hook)return Se(d.parse(t));const r=(e,p)=>Se(d.parse(d.merge(e,p)));return Object.assign(r,{endpoint:d,defaults:e.bind(null,d)}),t.request.hook(r,t)}),{endpoint:d,defaults:e.bind(null,d)})}(_,{headers:{"user-agent":`octokit-request.js/6.2.8 ${d()}`}});var Ae=class extends Error{constructor(e,p,a){super("Request failed due to following response errors:\n"+a.errors.map((e=>` - ${e.message}`)).join("\n")),this.request=e,this.headers=p,this.response=a,this.name="GraphqlResponseError",this.errors=a.errors,this.data=a.data,Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)}},Ne=["method","baseUrl","url","headers","request","query","mediaType"],Pe=["query","method","url"],Oe=/\/api\/v3\/?$/;function Ve(e,p){const a=e.defaults(p);return Object.assign(((e,p)=>function(e,p,a){if(a){if("string"==typeof p&&"query"in a)return Promise.reject(new Error('[@octokit/graphql] "query" cannot be used as variable name'));for(const e in a)if(Pe.includes(e))return Promise.reject(new Error(`[@octokit/graphql] "${e}" cannot be used as variable name`))}const d="string"==typeof p?Object.assign({query:p},a):p,t=Object.keys(d).reduce(((e,p)=>Ne.includes(p)?(e[p]=d[p],e):(e.variables||(e.variables={}),e.variables[p]=d[p],e)),{}),r=d.baseUrl||e.endpoint.DEFAULTS.baseUrl;return Oe.test(r)&&(t.url=r.replace(Oe,"/api/graphql")),e(t).then((e=>{if(e.data.errors){const p={};for(const a of Object.keys(e.headers))p[a]=e.headers[a];throw new Ae(t,p,e.data)}return e.data.data}))}(a,e,p)),{defaults:Ve.bind(null,a),endpoint:a.endpoint})}Ve(De,{headers:{"user-agent":`octokit-graphql.js/5.0.6 ${d()}`},method:"POST",url:"/graphql"});const Ce=/^v1\./,Fe=/^ghs_/,Ge=/^ghu_/;async function Re(e){const p=3===e.split(/\./).length,a=Ce.test(e)||Fe.test(e),d=Ge.test(e);return{type:"token",token:e,tokenType:p?"app":a?"installation":d?"user-to-server":"oauth"}}async function Ue(e,p,a,d){const t=p.endpoint.merge(a,d);return t.headers.authorization=function(e){return 3===e.split(/\./).length?`bearer ${e}`:`token ${e}`}(e),p(t)}const Le=function(e){if(!e)throw new Error("[@octokit/auth-token] No token passed to createTokenAuth");if("string"!=typeof e)throw new Error("[@octokit/auth-token] Token passed to createTokenAuth is not a string");return e=e.replace(/^(token|bearer) +/i,""),Object.assign(Re.bind(null,e),{hook:Ue.bind(null,e)})};var xe="4.2.4",Be=class{static defaults(e){return class extends(this){constructor(...p){const a=p[0]||{};super("function"!=typeof e?Object.assign({},e,a,a.userAgent&&e.userAgent?{userAgent:`${a.userAgent} ${e.userAgent}`}:null):e(a))}}}static plugin(...e){var p;const a=this.plugins;return(p=class extends(this){}).plugins=a.concat(e.filter((e=>!a.includes(e)))),p}constructor(e={}){const p=new t.Collection,a={baseUrl:De.endpoint.DEFAULTS.baseUrl,headers:{},request:Object.assign({},e.request,{hook:p.bind(null,"request")}),mediaType:{previews:[],format:""}};var r;if(a.headers["user-agent"]=[e.userAgent,`octokit-core.js/${xe} ${d()}`].filter(Boolean).join(" "),e.baseUrl&&(a.baseUrl=e.baseUrl),e.previews&&(a.mediaType.previews=e.previews),e.timeZone&&(a.headers["time-zone"]=e.timeZone),this.request=De.defaults(a),this.graphql=(r=this.request,Ve(r,{method:"POST",url:"/graphql"})).defaults(a),this.log=Object.assign({debug:()=>{},info:()=>{},warn:console.warn.bind(console),error:console.error.bind(console)},e.log),this.hook=p,e.authStrategy){const{authStrategy:a,...d}=e,t=a(Object.assign({request:this.request,log:this.log,octokit:this,octokitOptions:d},e.auth));p.wrap("request",t.hook),this.auth=t}else if(e.auth){const a=Le(e.auth);p.wrap("request",a.hook),this.auth=a}else this.auth=async()=>({type:"unauthenticated"});this.constructor.plugins.forEach((p=>{Object.assign(this,p(this,e))}))}};function je(e){e.hook.wrap("request",((p,a)=>{e.log.debug("request",a);const d=Date.now(),t=e.request.endpoint.parse(a),r=t.url.replace(a.baseUrl,"");return p(a).then((p=>(e.log.info(`${t.method} ${r} - ${p.status} in ${Date.now()-d}ms`),p))).catch((p=>{throw e.log.info(`${t.method} ${r} - ${p.status} in ${Date.now()-d}ms`),p}))}))}function Ie(e,p,a){const d="function"==typeof p?p.endpoint(a):e.request.endpoint(p,a),t="function"==typeof p?p:e.request,r=d.method,i=d.headers;let s=d.url;return{[Symbol.asyncIterator]:()=>({async next(){if(!s)return{done:!0};try{const e=function(e){if(!e.data)return{...e,data:[]};if(!("total_count"in e.data)||"url"in e.data)return e;const p=e.data.incomplete_results,a=e.data.repository_selection,d=e.data.total_count;delete e.data.incomplete_results,delete e.data.repository_selection,delete e.data.total_count;const t=Object.keys(e.data)[0],r=e.data[t];return e.data=r,void 0!==p&&(e.data.incomplete_results=p),void 0!==a&&(e.data.repository_selection=a),e.data.total_count=d,e}(await t({method:r,url:s,headers:i}));return s=((e.headers.link||"").match(/<([^>]+)>;\s*rel="next"/)||[])[1],{value:e}}catch(e){if(409!==e.status)throw e;return s="",{value:{status:200,headers:{},data:[]}}}}})}}function qe(e,p,a,d){return"function"==typeof a&&(d=a,a=void 0),$e(e,[],Ie(e,p,a)[Symbol.asyncIterator](),d)}function $e(e,p,a,d){return a.next().then((t=>{if(t.done)return p;let r=!1;return p=p.concat(d?d(t.value,(function(){r=!0})):t.value.data),r?p:$e(e,p,a,d)}))}function He(e){return{paginate:Object.assign(qe.bind(null,e),{iterator:Ie.bind(null,e)})}}Be.VERSION=xe,Be.plugins=[],je.VERSION="1.0.4",Object.assign(qe,{iterator:Ie}),He.VERSION="4.3.1";const Me={actions:{addCustomLabelsToSelfHostedRunnerForOrg:["POST /orgs/{org}/actions/runners/{runner_id}/labels"],addCustomLabelsToSelfHostedRunnerForRepo:["POST /repos/{owner}/{repo}/actions/runners/{runner_id}/labels"],addSelectedRepoToOrgSecret:["PUT /orgs/{org}/actions/secrets/{secret_name}/repositories/{repository_id}"],approveWorkflowRun:["POST /repos/{owner}/{repo}/actions/runs/{run_id}/approve"],cancelWorkflowRun:["POST /repos/{owner}/{repo}/actions/runs/{run_id}/cancel"],createOrUpdateEnvironmentSecret:["PUT /repositories/{repository_id}/environments/{environment_name}/secrets/{secret_name}"],createOrUpdateOrgSecret:["PUT /orgs/{org}/actions/secrets/{secret_name}"],createOrUpdateRepoSecret:["PUT /repos/{owner}/{repo}/actions/secrets/{secret_name}"],createRegistrationTokenForOrg:["POST /orgs/{org}/actions/runners/registration-token"],createRegistrationTokenForRepo:["POST /repos/{owner}/{repo}/actions/runners/registration-token"],createRemoveTokenForOrg:["POST /orgs/{org}/actions/runners/remove-token"],createRemoveTokenForRepo:["POST /repos/{owner}/{repo}/actions/runners/remove-token"],createWorkflowDispatch:["POST /repos/{owner}/{repo}/actions/workflows/{workflow_id}/dispatches"],deleteActionsCacheById:["DELETE /repos/{owner}/{repo}/actions/caches/{cache_id}"],deleteActionsCacheByKey:["DELETE /repos/{owner}/{repo}/actions/caches{?key,ref}"],deleteArtifact:["DELETE /repos/{owner}/{repo}/actions/artifacts/{artifact_id}"],deleteEnvironmentSecret:["DELETE /repositories/{repository_id}/environments/{environment_name}/secrets/{secret_name}"],deleteOrgSecret:["DELETE /orgs/{org}/actions/secrets/{secret_name}"],deleteRepoSecret:["DELETE /repos/{owner}/{repo}/actions/secrets/{secret_name}"],deleteSelfHostedRunnerFromOrg:["DELETE /orgs/{org}/actions/runners/{runner_id}"],deleteSelfHostedRunnerFromRepo:["DELETE /repos/{owner}/{repo}/actions/runners/{runner_id}"],deleteWorkflowRun:["DELETE /repos/{owner}/{repo}/actions/runs/{run_id}"],deleteWorkflowRunLogs:["DELETE /repos/{owner}/{repo}/actions/runs/{run_id}/logs"],disableSelectedRepositoryGithubActionsOrganization:["DELETE /orgs/{org}/actions/permissions/repositories/{repository_id}"],disableWorkflow:["PUT /repos/{owner}/{repo}/actions/workflows/{workflow_id}/disable"],downloadArtifact:["GET /repos/{owner}/{repo}/actions/artifacts/{artifact_id}/{archive_format}"],downloadJobLogsForWorkflowRun:["GET /repos/{owner}/{repo}/actions/jobs/{job_id}/logs"],downloadWorkflowRunAttemptLogs:["GET /repos/{owner}/{repo}/actions/runs/{run_id}/attempts/{attempt_number}/logs"],downloadWorkflowRunLogs:["GET /repos/{owner}/{repo}/actions/runs/{run_id}/logs"],enableSelectedRepositoryGithubActionsOrganization:["PUT /orgs/{org}/actions/permissions/repositories/{repository_id}"],enableWorkflow:["PUT /repos/{owner}/{repo}/actions/workflows/{workflow_id}/enable"],getActionsCacheList:["GET /repos/{owner}/{repo}/actions/caches"],getActionsCacheUsage:["GET /repos/{owner}/{repo}/actions/cache/usage"],getActionsCacheUsageByRepoForOrg:["GET /orgs/{org}/actions/cache/usage-by-repository"],getActionsCacheUsageForEnterprise:["GET /enterprises/{enterprise}/actions/cache/usage"],getActionsCacheUsageForOrg:["GET /orgs/{org}/actions/cache/usage"],getAllowedActionsOrganization:["GET /orgs/{org}/actions/permissions/selected-actions"],getAllowedActionsRepository:["GET /repos/{owner}/{repo}/actions/permissions/selected-actions"],getArtifact:["GET /repos/{owner}/{repo}/actions/artifacts/{artifact_id}"],getEnvironmentPublicKey:["GET /repositories/{repository_id}/environments/{environment_name}/secrets/public-key"],getEnvironmentSecret:["GET /repositories/{repository_id}/environments/{environment_name}/secrets/{secret_name}"],getGithubActionsDefaultWorkflowPermissionsEnterprise:["GET /enterprises/{enterprise}/actions/permissions/workflow"],getGithubActionsDefaultWorkflowPermissionsOrganization:["GET /orgs/{org}/actions/permissions/workflow"],getGithubActionsDefaultWorkflowPermissionsRepository:["GET /repos/{owner}/{repo}/actions/permissions/workflow"],getGithubActionsPermissionsOrganization:["GET /orgs/{org}/actions/permissions"],getGithubActionsPermissionsRepository:["GET /repos/{owner}/{repo}/actions/permissions"],getJobForWorkflowRun:["GET /repos/{owner}/{repo}/actions/jobs/{job_id}"],getOrgPublicKey:["GET /orgs/{org}/actions/secrets/public-key"],getOrgSecret:["GET /orgs/{org}/actions/secrets/{secret_name}"],getPendingDeploymentsForRun:["GET /repos/{owner}/{repo}/actions/runs/{run_id}/pending_deployments"],getRepoPermissions:["GET /repos/{owner}/{repo}/actions/permissions",{},{renamed:["actions","getGithubActionsPermissionsRepository"]}],getRepoPublicKey:["GET /repos/{owner}/{repo}/actions/secrets/public-key"],getRepoSecret:["GET /repos/{owner}/{repo}/actions/secrets/{secret_name}"],getReviewsForRun:["GET /repos/{owner}/{repo}/actions/runs/{run_id}/approvals"],getSelfHostedRunnerForOrg:["GET /orgs/{org}/actions/runners/{runner_id}"],getSelfHostedRunnerForRepo:["GET /repos/{owner}/{repo}/actions/runners/{runner_id}"],getWorkflow:["GET /repos/{owner}/{repo}/actions/workflows/{workflow_id}"],getWorkflowAccessToRepository:["GET /repos/{owner}/{repo}/actions/permissions/access"],getWorkflowRun:["GET /repos/{owner}/{repo}/actions/runs/{run_id}"],getWorkflowRunAttempt:["GET /repos/{owner}/{repo}/actions/runs/{run_id}/attempts/{attempt_number}"],getWorkflowRunUsage:["GET /repos/{owner}/{repo}/actions/runs/{run_id}/timing"],getWorkflowUsage:["GET /repos/{owner}/{repo}/actions/workflows/{workflow_id}/timing"],listArtifactsForRepo:["GET /repos/{owner}/{repo}/actions/artifacts"],listEnvironmentSecrets:["GET /repositories/{repository_id}/environments/{environment_name}/secrets"],listJobsForWorkflowRun:["GET /repos/{owner}/{repo}/actions/runs/{run_id}/jobs"],listJobsForWorkflowRunAttempt:["GET /repos/{owner}/{repo}/actions/runs/{run_id}/attempts/{attempt_number}/jobs"],listLabelsForSelfHostedRunnerForOrg:["GET /orgs/{org}/actions/runners/{runner_id}/labels"],listLabelsForSelfHostedRunnerForRepo:["GET /repos/{owner}/{repo}/actions/runners/{runner_id}/labels"],listOrgSecrets:["GET /orgs/{org}/actions/secrets"],listRepoSecrets:["GET /repos/{owner}/{repo}/actions/secrets"],listRepoWorkflows:["GET /repos/{owner}/{repo}/actions/workflows"],listRunnerApplicationsForOrg:["GET /orgs/{org}/actions/runners/downloads"],listRunnerApplicationsForRepo:["GET /repos/{owner}/{repo}/actions/runners/downloads"],listSelectedReposForOrgSecret:["GET /orgs/{org}/actions/secrets/{secret_name}/repositories"],listSelectedRepositoriesEnabledGithubActionsOrganization:["GET /orgs/{org}/actions/permissions/repositories"],listSelfHostedRunnersForOrg:["GET /orgs/{org}/actions/runners"],listSelfHostedRunnersForRepo:["GET /repos/{owner}/{repo}/actions/runners"],listWorkflowRunArtifacts:["GET /repos/{owner}/{repo}/actions/runs/{run_id}/artifacts"],listWorkflowRuns:["GET /repos/{owner}/{repo}/actions/workflows/{workflow_id}/runs"],listWorkflowRunsForRepo:["GET /repos/{owner}/{repo}/actions/runs"],reRunJobForWorkflowRun:["POST /repos/{owner}/{repo}/actions/jobs/{job_id}/rerun"],reRunWorkflow:["POST /repos/{owner}/{repo}/actions/runs/{run_id}/rerun"],reRunWorkflowFailedJobs:["POST /repos/{owner}/{repo}/actions/runs/{run_id}/rerun-failed-jobs"],removeAllCustomLabelsFromSelfHostedRunnerForOrg:["DELETE /orgs/{org}/actions/runners/{runner_id}/labels"],removeAllCustomLabelsFromSelfHostedRunnerForRepo:["DELETE /repos/{owner}/{repo}/actions/runners/{runner_id}/labels"],removeCustomLabelFromSelfHostedRunnerForOrg:["DELETE /orgs/{org}/actions/runners/{runner_id}/labels/{name}"],removeCustomLabelFromSelfHostedRunnerForRepo:["DELETE /repos/{owner}/{repo}/actions/runners/{runner_id}/labels/{name}"],removeSelectedRepoFromOrgSecret:["DELETE /orgs/{org}/actions/secrets/{secret_name}/repositories/{repository_id}"],reviewPendingDeploymentsForRun:["POST /repos/{owner}/{repo}/actions/runs/{run_id}/pending_deployments"],setAllowedActionsOrganization:["PUT /orgs/{org}/actions/permissions/selected-actions"],setAllowedActionsRepository:["PUT /repos/{owner}/{repo}/actions/permissions/selected-actions"],setCustomLabelsForSelfHostedRunnerForOrg:["PUT /orgs/{org}/actions/runners/{runner_id}/labels"],setCustomLabelsForSelfHostedRunnerForRepo:["PUT /repos/{owner}/{repo}/actions/runners/{runner_id}/labels"],setGithubActionsDefaultWorkflowPermissionsEnterprise:["PUT /enterprises/{enterprise}/actions/permissions/workflow"],setGithubActionsDefaultWorkflowPermissionsOrganization:["PUT /orgs/{org}/actions/permissions/workflow"],setGithubActionsDefaultWorkflowPermissionsRepository:["PUT /repos/{owner}/{repo}/actions/permissions/workflow"],setGithubActionsPermissionsOrganization:["PUT /orgs/{org}/actions/permissions"],setGithubActionsPermissionsRepository:["PUT /repos/{owner}/{repo}/actions/permissions"],setSelectedReposForOrgSecret:["PUT /orgs/{org}/actions/secrets/{secret_name}/repositories"],setSelectedRepositoriesEnabledGithubActionsOrganization:["PUT /orgs/{org}/actions/permissions/repositories"],setWorkflowAccessToRepository:["PUT /repos/{owner}/{repo}/actions/permissions/access"]},activity:{checkRepoIsStarredByAuthenticatedUser:["GET /user/starred/{owner}/{repo}"],deleteRepoSubscription:["DELETE /repos/{owner}/{repo}/subscription"],deleteThreadSubscription:["DELETE /notifications/threads/{thread_id}/subscription"],getFeeds:["GET /feeds"],getRepoSubscription:["GET /repos/{owner}/{repo}/subscription"],getThread:["GET /notifications/threads/{thread_id}"],getThreadSubscriptionForAuthenticatedUser:["GET /notifications/threads/{thread_id}/subscription"],listEventsForAuthenticatedUser:["GET /users/{username}/events"],listNotificationsForAuthenticatedUser:["GET /notifications"],listOrgEventsForAuthenticatedUser:["GET /users/{username}/events/orgs/{org}"],listPublicEvents:["GET /events"],listPublicEventsForRepoNetwork:["GET /networks/{owner}/{repo}/events"],listPublicEventsForUser:["GET /users/{username}/events/public"],listPublicOrgEvents:["GET /orgs/{org}/events"],listReceivedEventsForUser:["GET /users/{username}/received_events"],listReceivedPublicEventsForUser:["GET /users/{username}/received_events/public"],listRepoEvents:["GET /repos/{owner}/{repo}/events"],listRepoNotificationsForAuthenticatedUser:["GET /repos/{owner}/{repo}/notifications"],listReposStarredByAuthenticatedUser:["GET /user/starred"],listReposStarredByUser:["GET /users/{username}/starred"],listReposWatchedByUser:["GET /users/{username}/subscriptions"],listStargazersForRepo:["GET /repos/{owner}/{repo}/stargazers"],listWatchedReposForAuthenticatedUser:["GET /user/subscriptions"],listWatchersForRepo:["GET /repos/{owner}/{repo}/subscribers"],markNotificationsAsRead:["PUT /notifications"],markRepoNotificationsAsRead:["PUT /repos/{owner}/{repo}/notifications"],markThreadAsRead:["PATCH /notifications/threads/{thread_id}"],setRepoSubscription:["PUT /repos/{owner}/{repo}/subscription"],setThreadSubscription:["PUT /notifications/threads/{thread_id}/subscription"],starRepoForAuthenticatedUser:["PUT /user/starred/{owner}/{repo}"],unstarRepoForAuthenticatedUser:["DELETE /user/starred/{owner}/{repo}"]},apps:{addRepoToInstallation:["PUT /user/installations/{installation_id}/repositories/{repository_id}",{},{renamed:["apps","addRepoToInstallationForAuthenticatedUser"]}],addRepoToInstallationForAuthenticatedUser:["PUT /user/installations/{installation_id}/repositories/{repository_id}"],checkToken:["POST /applications/{client_id}/token"],createFromManifest:["POST /app-manifests/{code}/conversions"],createInstallationAccessToken:["POST /app/installations/{installation_id}/access_tokens"],deleteAuthorization:["DELETE /applications/{client_id}/grant"],deleteInstallation:["DELETE /app/installations/{installation_id}"],deleteToken:["DELETE /applications/{client_id}/token"],getAuthenticated:["GET /app"],getBySlug:["GET /apps/{app_slug}"],getInstallation:["GET /app/installations/{installation_id}"],getOrgInstallation:["GET /orgs/{org}/installation"],getRepoInstallation:["GET /repos/{owner}/{repo}/installation"],getSubscriptionPlanForAccount:["GET /marketplace_listing/accounts/{account_id}"],getSubscriptionPlanForAccountStubbed:["GET /marketplace_listing/stubbed/accounts/{account_id}"],getUserInstallation:["GET /users/{username}/installation"],getWebhookConfigForApp:["GET /app/hook/config"],getWebhookDelivery:["GET /app/hook/deliveries/{delivery_id}"],listAccountsForPlan:["GET /marketplace_listing/plans/{plan_id}/accounts"],listAccountsForPlanStubbed:["GET /marketplace_listing/stubbed/plans/{plan_id}/accounts"],listInstallationReposForAuthenticatedUser:["GET /user/installations/{installation_id}/repositories"],listInstallations:["GET /app/installations"],listInstallationsForAuthenticatedUser:["GET /user/installations"],listPlans:["GET /marketplace_listing/plans"],listPlansStubbed:["GET /marketplace_listing/stubbed/plans"],listReposAccessibleToInstallation:["GET /installation/repositories"],listSubscriptionsForAuthenticatedUser:["GET /user/marketplace_purchases"],listSubscriptionsForAuthenticatedUserStubbed:["GET /user/marketplace_purchases/stubbed"],listWebhookDeliveries:["GET /app/hook/deliveries"],redeliverWebhookDelivery:["POST /app/hook/deliveries/{delivery_id}/attempts"],removeRepoFromInstallation:["DELETE /user/installations/{installation_id}/repositories/{repository_id}",{},{renamed:["apps","removeRepoFromInstallationForAuthenticatedUser"]}],removeRepoFromInstallationForAuthenticatedUser:["DELETE /user/installations/{installation_id}/repositories/{repository_id}"],resetToken:["PATCH /applications/{client_id}/token"],revokeInstallationAccessToken:["DELETE /installation/token"],scopeToken:["POST /applications/{client_id}/token/scoped"],suspendInstallation:["PUT /app/installations/{installation_id}/suspended"],unsuspendInstallation:["DELETE /app/installations/{installation_id}/suspended"],updateWebhookConfigForApp:["PATCH /app/hook/config"]},billing:{getGithubActionsBillingOrg:["GET /orgs/{org}/settings/billing/actions"],getGithubActionsBillingUser:["GET /users/{username}/settings/billing/actions"],getGithubAdvancedSecurityBillingGhe:["GET /enterprises/{enterprise}/settings/billing/advanced-security"],getGithubAdvancedSecurityBillingOrg:["GET /orgs/{org}/settings/billing/advanced-security"],getGithubPackagesBillingOrg:["GET /orgs/{org}/settings/billing/packages"],getGithubPackagesBillingUser:["GET /users/{username}/settings/billing/packages"],getSharedStorageBillingOrg:["GET /orgs/{org}/settings/billing/shared-storage"],getSharedStorageBillingUser:["GET /users/{username}/settings/billing/shared-storage"]},checks:{create:["POST /repos/{owner}/{repo}/check-runs"],createSuite:["POST /repos/{owner}/{repo}/check-suites"],get:["GET /repos/{owner}/{repo}/check-runs/{check_run_id}"],getSuite:["GET /repos/{owner}/{repo}/check-suites/{check_suite_id}"],listAnnotations:["GET /repos/{owner}/{repo}/check-runs/{check_run_id}/annotations"],listForRef:["GET /repos/{owner}/{repo}/commits/{ref}/check-runs"],listForSuite:["GET /repos/{owner}/{repo}/check-suites/{check_suite_id}/check-runs"],listSuitesForRef:["GET /repos/{owner}/{repo}/commits/{ref}/check-suites"],rerequestRun:["POST /repos/{owner}/{repo}/check-runs/{check_run_id}/rerequest"],rerequestSuite:["POST /repos/{owner}/{repo}/check-suites/{check_suite_id}/rerequest"],setSuitesPreferences:["PATCH /repos/{owner}/{repo}/check-suites/preferences"],update:["PATCH /repos/{owner}/{repo}/check-runs/{check_run_id}"]},codeScanning:{deleteAnalysis:["DELETE /repos/{owner}/{repo}/code-scanning/analyses/{analysis_id}{?confirm_delete}"],getAlert:["GET /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}",{},{renamedParameters:{alert_id:"alert_number"}}],getAnalysis:["GET /repos/{owner}/{repo}/code-scanning/analyses/{analysis_id}"],getCodeqlDatabase:["GET /repos/{owner}/{repo}/code-scanning/codeql/databases/{language}"],getSarif:["GET /repos/{owner}/{repo}/code-scanning/sarifs/{sarif_id}"],listAlertInstances:["GET /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}/instances"],listAlertsForEnterprise:["GET /enterprises/{enterprise}/code-scanning/alerts"],listAlertsForOrg:["GET /orgs/{org}/code-scanning/alerts"],listAlertsForRepo:["GET /repos/{owner}/{repo}/code-scanning/alerts"],listAlertsInstances:["GET /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}/instances",{},{renamed:["codeScanning","listAlertInstances"]}],listCodeqlDatabases:["GET /repos/{owner}/{repo}/code-scanning/codeql/databases"],listRecentAnalyses:["GET /repos/{owner}/{repo}/code-scanning/analyses"],updateAlert:["PATCH /repos/{owner}/{repo}/code-scanning/alerts/{alert_number}"],uploadSarif:["POST /repos/{owner}/{repo}/code-scanning/sarifs"]},codesOfConduct:{getAllCodesOfConduct:["GET /codes_of_conduct"],getConductCode:["GET /codes_of_conduct/{key}"]},codespaces:{addRepositoryForSecretForAuthenticatedUser:["PUT /user/codespaces/secrets/{secret_name}/repositories/{repository_id}"],addSelectedRepoToOrgSecret:["PUT /organizations/{org}/codespaces/secrets/{secret_name}/repositories/{repository_id}"],codespaceMachinesForAuthenticatedUser:["GET /user/codespaces/{codespace_name}/machines"],createForAuthenticatedUser:["POST /user/codespaces"],createOrUpdateOrgSecret:["PUT /organizations/{org}/codespaces/secrets/{secret_name}"],createOrUpdateRepoSecret:["PUT /repos/{owner}/{repo}/codespaces/secrets/{secret_name}"],createOrUpdateSecretForAuthenticatedUser:["PUT /user/codespaces/secrets/{secret_name}"],createWithPrForAuthenticatedUser:["POST /repos/{owner}/{repo}/pulls/{pull_number}/codespaces"],createWithRepoForAuthenticatedUser:["POST /repos/{owner}/{repo}/codespaces"],deleteForAuthenticatedUser:["DELETE /user/codespaces/{codespace_name}"],deleteFromOrganization:["DELETE /orgs/{org}/members/{username}/codespaces/{codespace_name}"],deleteOrgSecret:["DELETE /organizations/{org}/codespaces/secrets/{secret_name}"],deleteRepoSecret:["DELETE /repos/{owner}/{repo}/codespaces/secrets/{secret_name}"],deleteSecretForAuthenticatedUser:["DELETE /user/codespaces/secrets/{secret_name}"],exportForAuthenticatedUser:["POST /user/codespaces/{codespace_name}/exports"],getExportDetailsForAuthenticatedUser:["GET /user/codespaces/{codespace_name}/exports/{export_id}"],getForAuthenticatedUser:["GET /user/codespaces/{codespace_name}"],getOrgPublicKey:["GET /organizations/{org}/codespaces/secrets/public-key"],getOrgSecret:["GET /organizations/{org}/codespaces/secrets/{secret_name}"],getPublicKeyForAuthenticatedUser:["GET /user/codespaces/secrets/public-key"],getRepoPublicKey:["GET /repos/{owner}/{repo}/codespaces/secrets/public-key"],getRepoSecret:["GET /repos/{owner}/{repo}/codespaces/secrets/{secret_name}"],getSecretForAuthenticatedUser:["GET /user/codespaces/secrets/{secret_name}"],listDevcontainersInRepositoryForAuthenticatedUser:["GET /repos/{owner}/{repo}/codespaces/devcontainers"],listForAuthenticatedUser:["GET /user/codespaces"],listInOrganization:["GET /orgs/{org}/codespaces",{},{renamedParameters:{org_id:"org"}}],listInRepositoryForAuthenticatedUser:["GET /repos/{owner}/{repo}/codespaces"],listOrgSecrets:["GET /organizations/{org}/codespaces/secrets"],listRepoSecrets:["GET /repos/{owner}/{repo}/codespaces/secrets"],listRepositoriesForSecretForAuthenticatedUser:["GET /user/codespaces/secrets/{secret_name}/repositories"],listSecretsForAuthenticatedUser:["GET /user/codespaces/secrets"],listSelectedReposForOrgSecret:["GET /organizations/{org}/codespaces/secrets/{secret_name}/repositories"],preFlightWithRepoForAuthenticatedUser:["GET /repos/{owner}/{repo}/codespaces/new"],removeRepositoryForSecretForAuthenticatedUser:["DELETE /user/codespaces/secrets/{secret_name}/repositories/{repository_id}"],removeSelectedRepoFromOrgSecret:["DELETE /organizations/{org}/codespaces/secrets/{secret_name}/repositories/{repository_id}"],repoMachinesForAuthenticatedUser:["GET /repos/{owner}/{repo}/codespaces/machines"],setRepositoriesForSecretForAuthenticatedUser:["PUT /user/codespaces/secrets/{secret_name}/repositories"],setSelectedReposForOrgSecret:["PUT /organizations/{org}/codespaces/secrets/{secret_name}/repositories"],startForAuthenticatedUser:["POST /user/codespaces/{codespace_name}/start"],stopForAuthenticatedUser:["POST /user/codespaces/{codespace_name}/stop"],stopInOrganization:["POST /orgs/{org}/members/{username}/codespaces/{codespace_name}/stop"],updateForAuthenticatedUser:["PATCH /user/codespaces/{codespace_name}"]},dependabot:{addSelectedRepoToOrgSecret:["PUT /orgs/{org}/dependabot/secrets/{secret_name}/repositories/{repository_id}"],createOrUpdateOrgSecret:["PUT /orgs/{org}/dependabot/secrets/{secret_name}"],createOrUpdateRepoSecret:["PUT /repos/{owner}/{repo}/dependabot/secrets/{secret_name}"],deleteOrgSecret:["DELETE /orgs/{org}/dependabot/secrets/{secret_name}"],deleteRepoSecret:["DELETE /repos/{owner}/{repo}/dependabot/secrets/{secret_name}"],getAlert:["GET /repos/{owner}/{repo}/dependabot/alerts/{alert_number}"],getOrgPublicKey:["GET /orgs/{org}/dependabot/secrets/public-key"],getOrgSecret:["GET /orgs/{org}/dependabot/secrets/{secret_name}"],getRepoPublicKey:["GET /repos/{owner}/{repo}/dependabot/secrets/public-key"],getRepoSecret:["GET /repos/{owner}/{repo}/dependabot/secrets/{secret_name}"],listAlertsForRepo:["GET /repos/{owner}/{repo}/dependabot/alerts"],listOrgSecrets:["GET /orgs/{org}/dependabot/secrets"],listRepoSecrets:["GET /repos/{owner}/{repo}/dependabot/secrets"],listSelectedReposForOrgSecret:["GET /orgs/{org}/dependabot/secrets/{secret_name}/repositories"],removeSelectedRepoFromOrgSecret:["DELETE /orgs/{org}/dependabot/secrets/{secret_name}/repositories/{repository_id}"],setSelectedReposForOrgSecret:["PUT /orgs/{org}/dependabot/secrets/{secret_name}/repositories"],updateAlert:["PATCH /repos/{owner}/{repo}/dependabot/alerts/{alert_number}"]},dependencyGraph:{createRepositorySnapshot:["POST /repos/{owner}/{repo}/dependency-graph/snapshots"],diffRange:["GET /repos/{owner}/{repo}/dependency-graph/compare/{basehead}"]},emojis:{get:["GET /emojis"]},enterpriseAdmin:{addCustomLabelsToSelfHostedRunnerForEnterprise:["POST /enterprises/{enterprise}/actions/runners/{runner_id}/labels"],disableSelectedOrganizationGithubActionsEnterprise:["DELETE /enterprises/{enterprise}/actions/permissions/organizations/{org_id}"],enableSelectedOrganizationGithubActionsEnterprise:["PUT /enterprises/{enterprise}/actions/permissions/organizations/{org_id}"],getAllowedActionsEnterprise:["GET /enterprises/{enterprise}/actions/permissions/selected-actions"],getGithubActionsPermissionsEnterprise:["GET /enterprises/{enterprise}/actions/permissions"],getServerStatistics:["GET /enterprise-installation/{enterprise_or_org}/server-statistics"],listLabelsForSelfHostedRunnerForEnterprise:["GET /enterprises/{enterprise}/actions/runners/{runner_id}/labels"],listSelectedOrganizationsEnabledGithubActionsEnterprise:["GET /enterprises/{enterprise}/actions/permissions/organizations"],removeAllCustomLabelsFromSelfHostedRunnerForEnterprise:["DELETE /enterprises/{enterprise}/actions/runners/{runner_id}/labels"],removeCustomLabelFromSelfHostedRunnerForEnterprise:["DELETE /enterprises/{enterprise}/actions/runners/{runner_id}/labels/{name}"],setAllowedActionsEnterprise:["PUT /enterprises/{enterprise}/actions/permissions/selected-actions"],setCustomLabelsForSelfHostedRunnerForEnterprise:["PUT /enterprises/{enterprise}/actions/runners/{runner_id}/labels"],setGithubActionsPermissionsEnterprise:["PUT /enterprises/{enterprise}/actions/permissions"],setSelectedOrganizationsEnabledGithubActionsEnterprise:["PUT /enterprises/{enterprise}/actions/permissions/organizations"]},gists:{checkIsStarred:["GET /gists/{gist_id}/star"],create:["POST /gists"],createComment:["POST /gists/{gist_id}/comments"],delete:["DELETE /gists/{gist_id}"],deleteComment:["DELETE /gists/{gist_id}/comments/{comment_id}"],fork:["POST /gists/{gist_id}/forks"],get:["GET /gists/{gist_id}"],getComment:["GET /gists/{gist_id}/comments/{comment_id}"],getRevision:["GET /gists/{gist_id}/{sha}"],list:["GET /gists"],listComments:["GET /gists/{gist_id}/comments"],listCommits:["GET /gists/{gist_id}/commits"],listForUser:["GET /users/{username}/gists"],listForks:["GET /gists/{gist_id}/forks"],listPublic:["GET /gists/public"],listStarred:["GET /gists/starred"],star:["PUT /gists/{gist_id}/star"],unstar:["DELETE /gists/{gist_id}/star"],update:["PATCH /gists/{gist_id}"],updateComment:["PATCH /gists/{gist_id}/comments/{comment_id}"]},git:{createBlob:["POST /repos/{owner}/{repo}/git/blobs"],createCommit:["POST /repos/{owner}/{repo}/git/commits"],createRef:["POST /repos/{owner}/{repo}/git/refs"],createTag:["POST /repos/{owner}/{repo}/git/tags"],createTree:["POST /repos/{owner}/{repo}/git/trees"],deleteRef:["DELETE /repos/{owner}/{repo}/git/refs/{ref}"],getBlob:["GET /repos/{owner}/{repo}/git/blobs/{file_sha}"],getCommit:["GET /repos/{owner}/{repo}/git/commits/{commit_sha}"],getRef:["GET /repos/{owner}/{repo}/git/ref/{ref}"],getTag:["GET /repos/{owner}/{repo}/git/tags/{tag_sha}"],getTree:["GET /repos/{owner}/{repo}/git/trees/{tree_sha}"],listMatchingRefs:["GET /repos/{owner}/{repo}/git/matching-refs/{ref}"],updateRef:["PATCH /repos/{owner}/{repo}/git/refs/{ref}"]},gitignore:{getAllTemplates:["GET /gitignore/templates"],getTemplate:["GET /gitignore/templates/{name}"]},interactions:{getRestrictionsForAuthenticatedUser:["GET /user/interaction-limits"],getRestrictionsForOrg:["GET /orgs/{org}/interaction-limits"],getRestrictionsForRepo:["GET /repos/{owner}/{repo}/interaction-limits"],getRestrictionsForYourPublicRepos:["GET /user/interaction-limits",{},{renamed:["interactions","getRestrictionsForAuthenticatedUser"]}],removeRestrictionsForAuthenticatedUser:["DELETE /user/interaction-limits"],removeRestrictionsForOrg:["DELETE /orgs/{org}/interaction-limits"],removeRestrictionsForRepo:["DELETE /repos/{owner}/{repo}/interaction-limits"],removeRestrictionsForYourPublicRepos:["DELETE /user/interaction-limits",{},{renamed:["interactions","removeRestrictionsForAuthenticatedUser"]}],setRestrictionsForAuthenticatedUser:["PUT /user/interaction-limits"],setRestrictionsForOrg:["PUT /orgs/{org}/interaction-limits"],setRestrictionsForRepo:["PUT /repos/{owner}/{repo}/interaction-limits"],setRestrictionsForYourPublicRepos:["PUT /user/interaction-limits",{},{renamed:["interactions","setRestrictionsForAuthenticatedUser"]}]},issues:{addAssignees:["POST /repos/{owner}/{repo}/issues/{issue_number}/assignees"],addLabels:["POST /repos/{owner}/{repo}/issues/{issue_number}/labels"],checkUserCanBeAssigned:["GET /repos/{owner}/{repo}/assignees/{assignee}"],create:["POST /repos/{owner}/{repo}/issues"],createComment:["POST /repos/{owner}/{repo}/issues/{issue_number}/comments"],createLabel:["POST /repos/{owner}/{repo}/labels"],createMilestone:["POST /repos/{owner}/{repo}/milestones"],deleteComment:["DELETE /repos/{owner}/{repo}/issues/comments/{comment_id}"],deleteLabel:["DELETE /repos/{owner}/{repo}/labels/{name}"],deleteMilestone:["DELETE /repos/{owner}/{repo}/milestones/{milestone_number}"],get:["GET /repos/{owner}/{repo}/issues/{issue_number}"],getComment:["GET /repos/{owner}/{repo}/issues/comments/{comment_id}"],getEvent:["GET /repos/{owner}/{repo}/issues/events/{event_id}"],getLabel:["GET /repos/{owner}/{repo}/labels/{name}"],getMilestone:["GET /repos/{owner}/{repo}/milestones/{milestone_number}"],list:["GET /issues"],listAssignees:["GET /repos/{owner}/{repo}/assignees"],listComments:["GET /repos/{owner}/{repo}/issues/{issue_number}/comments"],listCommentsForRepo:["GET /repos/{owner}/{repo}/issues/comments"],listEvents:["GET /repos/{owner}/{repo}/issues/{issue_number}/events"],listEventsForRepo:["GET /repos/{owner}/{repo}/issues/events"],listEventsForTimeline:["GET /repos/{owner}/{repo}/issues/{issue_number}/timeline"],listForAuthenticatedUser:["GET /user/issues"],listForOrg:["GET /orgs/{org}/issues"],listForRepo:["GET /repos/{owner}/{repo}/issues"],listLabelsForMilestone:["GET /repos/{owner}/{repo}/milestones/{milestone_number}/labels"],listLabelsForRepo:["GET /repos/{owner}/{repo}/labels"],listLabelsOnIssue:["GET /repos/{owner}/{repo}/issues/{issue_number}/labels"],listMilestones:["GET /repos/{owner}/{repo}/milestones"],lock:["PUT /repos/{owner}/{repo}/issues/{issue_number}/lock"],removeAllLabels:["DELETE /repos/{owner}/{repo}/issues/{issue_number}/labels"],removeAssignees:["DELETE /repos/{owner}/{repo}/issues/{issue_number}/assignees"],removeLabel:["DELETE /repos/{owner}/{repo}/issues/{issue_number}/labels/{name}"],setLabels:["PUT /repos/{owner}/{repo}/issues/{issue_number}/labels"],unlock:["DELETE /repos/{owner}/{repo}/issues/{issue_number}/lock"],update:["PATCH /repos/{owner}/{repo}/issues/{issue_number}"],updateComment:["PATCH /repos/{owner}/{repo}/issues/comments/{comment_id}"],updateLabel:["PATCH /repos/{owner}/{repo}/labels/{name}"],updateMilestone:["PATCH /repos/{owner}/{repo}/milestones/{milestone_number}"]},licenses:{get:["GET /licenses/{license}"],getAllCommonlyUsed:["GET /licenses"],getForRepo:["GET /repos/{owner}/{repo}/license"]},markdown:{render:["POST /markdown"],renderRaw:["POST /markdown/raw",{headers:{"content-type":"text/plain; charset=utf-8"}}]},meta:{get:["GET /meta"],getOctocat:["GET /octocat"],getZen:["GET /zen"],root:["GET /"]},migrations:{cancelImport:["DELETE /repos/{owner}/{repo}/import"],deleteArchiveForAuthenticatedUser:["DELETE /user/migrations/{migration_id}/archive"],deleteArchiveForOrg:["DELETE /orgs/{org}/migrations/{migration_id}/archive"],downloadArchiveForOrg:["GET /orgs/{org}/migrations/{migration_id}/archive"],getArchiveForAuthenticatedUser:["GET /user/migrations/{migration_id}/archive"],getCommitAuthors: <AUTHORS>
//# sourceMappingURL=https://cursor-sourcemaps.s3.amazonaws.com/sourcemaps/b6fb41b5f36bda05cab7109606e7404a65d1ff30/extensions/configuration-editing/dist/configurationEditingMain.js.map
//# debugId=18fcbbf9-feff-5ce8-a8c0-ebf8f36906f2
