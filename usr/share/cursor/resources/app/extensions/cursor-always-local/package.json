{"name": "cursor-always-local", "description": "Implements experimentation features for Cursor", "author": "Anysphere, Inc.", "publisher": "anysphere", "version": "0.0.1", "private": true, "repository": {"type": "git", "url": "https://github.com/anysphere/vscode"}, "extensionKind": ["ui"], "engines": {"vscode": "^1.43.0", "yarn": "please-use-npm"}, "activationEvents": ["onStartupFinished", "onResolveRemoteAuthority:background-composer"], "enabledApiProposals": ["cursor", "control", "externalUriOpener", "contribSourceControlInputBoxMenu", "resolvers"], "main": "./dist/main", "contributes": {"keybindings": [], "menus": {"scm/inputBox": [{"command": "cursor.generateGitCommitMessage", "when": "scmProvider == git"}]}, "configuration": {"type": "object", "title": "Cursor Always Local"}}, "optionalDependencies": {"@vscode/windows-ca-certs": "^0.3.3"}}